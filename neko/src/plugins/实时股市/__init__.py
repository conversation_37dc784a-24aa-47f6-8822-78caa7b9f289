import asyncio
import time
import nonebot

from playwright.async_api import async_playwright
from typing import Optional, <PERSON><PERSON>
from nonebot import logger
from nonebot.plugin import PluginMetadata
from nonebot.plugin.on import on_fullmatch
from nonebot.adapters.onebot.v11 import Bot, Event
from nonebot.adapters.onebot.v11 import Message, MessageSegment
from nonebot_plugin_apscheduler import scheduler
from dataclasses import dataclass
from nonebot.plugin import PluginMetadata

__plugin_name__ = PluginMetadata(
		name="实时股市",
		description="每天定时发送，显示实时股市走势图",
		usage="用法：实时股市",
		type="application"
)


async def get_screenshots() -> Tuple[Optional[bytes], Optional[bytes]]:
	"""获取两个页面的截图字节数据
	返回: (地图截图数据, 热点数据截图数据)
	"""
	async with async_playwright() as p:
		browser = await p.chromium.launch(headless=True)
		context = await browser.new_context(
				viewport={"width": 3840, "height": 2160},
				device_scale_factor=2
		)
		page = await context.new_page()
		
		try:
			# 第一个页面截图
			logger.info("开始处理地图页面...")
			map_screenshot = await process_map_page(page)
			
			# 第二个页面截图
			logger.info("开始处理热点数据页面...")
			hot_screenshot = await process_hot_page(page)
			
			return map_screenshot, hot_screenshot
		
		except Exception as e:
			logger.error(f"截图过程中出错: {str(e)}")
			return (None, None)
		finally:
			await context.close()
			await browser.close()


async def process_map_page(page) -> Optional[bytes]:
	"""处理地图页面并返回截图"""
	try:
		page.set_default_timeout(60000)
		logger.info("访问实时股市页面...")
		await page.goto(
				"https://summary.jrj.com.cn/dataCenter/dpyt/",
				wait_until="networkidle"
		)
		
		# 处理广告
		await handle_popup(page)
		
		logger.info("定位地图元素...")
		map_element = page.locator('div#mapcontainment.map-con')
		await map_element.wait_for(state="visible")
		await map_element.scroll_into_view_if_needed()
		
		logger.info("截图地图元素...")
		return await map_element.screenshot(
				type="png",
				scale="device",
				omit_background=True
		)
	except Exception as e:
		logger.error(f"地图页面处理失败: {str(e)}")
		return None


async def process_hot_page(page) -> Optional[bytes]:
	"""处理热点数据页面并返回截图"""
	try:
		logger.info("访问热点数据页面...")
		await page.goto(
				"https://summary.jrj.com.cn/dataCenter/zdtwdj",
				wait_until="networkidle"
		)
		
		# 处理广告
		await handle_popup(page)
		
		logger.info("定位热点数据元素...")
		hot_element = page.locator('div.market-hot-wrapper')
		await hot_element.wait_for(state="visible")
		await hot_element.scroll_into_view_if_needed()
		
		logger.info("截图热点数据...")
		return await hot_element.screenshot(
				type="png",
				scale="device",
				omit_background=True
		)
	except Exception as e:
		logger.error(f"热点数据页面处理失败: {str(e)}")
		return None


async def handle_popup(page):
	"""通用广告弹窗处理"""
	try:
		close_btn = page.locator('img.close[src*="pop-download-close.png"]')
		await close_btn.wait_for(timeout=3000)
		await close_btn.click()
		logger.info("关闭广告弹窗...")
		await page.wait_for_timeout(1000)  # 等待关闭动画
	except:
		pass


async def job(gid: int):
	try:
		map_img, hot_img = await get_screenshots()
		if not map_img and not hot_img:
			logger.error("截图失败，请检查网络连接或联系管理员")
		else:
			# msg = MessageSegment.text("今日股市热点数据：") + MessageSegment.image(map_img) + MessageSegment.image(hot_img)
			# await nonebot.get_bot().send_group_msg(group_id=gid,
			#                                        message=msg)
			await nonebot.get_bot().send_group_msg(group_id=gid, message="今日股市热点数据：")
			if map_img:
				await nonebot.get_bot().send_group_msg(group_id=gid, message=MessageSegment.image(map_img))
			if hot_img:
				await nonebot.get_bot().send_group_msg(group_id=gid, message=MessageSegment.image(hot_img))
	
	except Exception as e:
		logger.error(f"实时股市插件出错: {str(e)}")


key = on_fullmatch(r"实时股市", priority=5)


@key.handle()
async def _(bot: Bot, event: Event):
	await job(event.group_id)


@dataclass
class Time:
	hour: int
	minute: int


plugin_config = [
		# [839497425, Time(10, 00)],  # 正人君子
		# [839497425, Time(15, 00)],  # 正人君子
		# [616978142, Time(10, 5)],  # 魔怔人
		# [616978142, Time(12, 5)],  # 魔怔人
		# [616978142, Time(15, 5)],  # 魔怔人
		# [868475842, Time(10, 10)],  # 龙河群
		# [868475842, Time(15, 10)],  # 龙河群
]
for gid, t in plugin_config:
	logger.opt(colors=True).info(
			f"<y>{gid}</y> <c>{t}</c>"
	)
	scheduler.add_job(job, "cron", hour=t.hour, minute=t.minute, id=str(time.time_ns()), args=[gid])
