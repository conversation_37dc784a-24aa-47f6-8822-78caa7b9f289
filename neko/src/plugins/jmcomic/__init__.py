import os
import random
import re
import shutil
import time
import zipfile
from pathlib import Path
from fpdf import FPDF
from PIL import Image
import jmcomic.jm_exception
from jmcomic import *
from nonebot.adapters.onebot.v11 import Bot, Event
from nonebot.permission import SUPERUSER
from nonebot.plugin import PluginMetadata
from nonebot.plugin.on import on_fullmatch, on_startswith
from nonebot_plugin_apscheduler import scheduler
from natsort import natsorted

__plugin_meta__ = PluginMetadata(
    name="JMComic本子",
    description="下载一个指定本子 | 随机一个正经人收藏本子",
    usage="本子1085899 | 随机本子",
    type="application",
)

comic_path = Path('/media/movies/nsfw/comic')
class Time:
    def __init__(self, hour, minute):
        self.hour: int = hour
        self.minute: int = minute

    def __str__(self):
        return f"{self.hour}:{self.minute}"


def images2pdf(image_folder: Path):
    """
    将指定文件夹中的图片转换为 PDF 文件，每页 PDF 对应一张图片。
    第一页为文件夹名称的字符

    :param image_folder: 包含图片的文件夹路径
    :return: 生成的 PDF 文件路径
    """
    output_pdf = image_folder.joinpath('cache.pdf')

    def get_images(folder: Path):
        imgs = []
        for child in folder.iterdir():
            if child.is_file():
                if child.suffix.lower() in ['.png', '.jpg', '.jpeg', '.bmp', '.gif', '.webp']:
                    imgs.append(child)
            else:
                imgs.extend(get_images(child))

        return natsorted(imgs)

    # 获取文件夹中的所有图片文件
    images = get_images(image_folder)  # 按文件名排序

    if not images:
        print("未找到图片文件！")
        return

    # 创建 PDF 对象
    pdf = FPDF(unit="pt")  # 使用点（point）作为单位，1点 = 1/72英寸
    pdf.add_font("NotoSansCJK", "", "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc", uni=True)
    pdf.set_font("NotoSansCJK", size=20)

    pdf.set_title(image_folder.name)

    for image in images:
        image_path = os.path.join(image_folder, image)
        try:
            # 打开图片
            img = Image.open(image_path)
            img_width, img_height = img.size

            # 设置 PDF 页面大小（与图片大小一致）
            pdf.add_page(format=(img_width, img_height))

            # 将图片添加到 PDF 页面
            pdf.image(image_path, x=0, y=0, w=img_width, h=img_height)
            # print(f"已添加图片: {image}")
        except Exception as e:
            print(f"无法处理图片 {image}: {e}")

    # 最后一页单独列出名字
    pdf.add_page()
    # print(image_folder.name)
    pdf.multi_cell(w=0, h=25, text=image_folder.name, ln=1, align="C")

    # 保存 PDF 文件
    pdf.output(output_pdf)
    print(f"PDF 文件已保存到: {output_pdf}")

    return Path(output_pdf)


def get_comic_favorite():
    folder_path = comic_path.joinpath('favorite')
    # artist = random.choice(list(folder_path.iterdir()))
    # album = random.choice(list(artist.iterdir()))
    album = random.choice([file for file in folder_path.rglob('*') if file.is_file()])

    # 解压zip
    with zipfile.ZipFile(album, 'r') as zip_ref:
        extract_path = folder_path.parent.joinpath('favorite').joinpath(album.stem)
        zip_ref.extractall(extract_path)

    return extract_path


async def send_comic(bot: Bot, gid, pdf_file: Path, pdf_title):
    await bot.send_group_msg(group_id=gid, message=f"文件准备完成，正在上传本子中...")

    if pdf_file.stat().st_size > 1024 * 1024 * 500:
        await bot.send_group_msg(group_id=gid, message="文件大于500MB啦，取消发送，你重新找一本吧")
        return
    else:
        try:
            await bot.upload_group_file(group_id=gid,
                                        file=pdf_file.as_uri(),
                                        name=f'{pdf_title}.pdf')
        except Exception as e:
            print(e)
            await bot.send_group_msg(group_id=gid, message="文件较大，正在上传")
        else:
            print(f"发送本子成功: {pdf_file}")

    shutil.rmtree(pdf_file.parent)

    print(f"压缩文件已删除: {pdf_file}")




key1 = on_fullmatch('随机本子', priority=10)


@key1.handle()
async def _(bot: Bot, event: Event):
    gid = event.group_id
    await send_comic(bot, gid, images2pdf(get_comic_favorite()), '正经人收藏的本子')


key2 = on_startswith('本子')


@key2.handle()
async def downloader(bot: Bot, event: Event) -> bool:
    singleEpOption = create_option_by_file('src/plugins/jmcomic/singleEpOption.yml')
    client = singleEpOption.new_jm_client()
    client.login('2539907983', '2817586858wzz')

    # 准备数据
    msg = str(event.get_message())

    try:
        aid = re.search(r'(?<=本子).\d+', msg).group()
    except AttributeError:
        await key2.finish('请输入正确的jm ID')

    try:
        album = client.get_album_detail(aid)
    except jmcomic.jm_exception.MissingAlbumPhotoException:
        await key2.finish(f'本子{aid}不存在')

    album_path = Path(f'/media/movies/nsfw/comic/others/{album.title}')

    if not album_path.exists():
        if album.episode_list.__len__() == 1:
            print(f'单集：{album.title}，aid：{aid}')
            singleEpOption.download_album(aid)
        else:
            print(f'多集：{album.title}，aid：{aid}')
            await key2.finish('避免文件太大，暂不支持多章节本子')
    else:
        print(f'已存在：{album.title}，aid：{aid}')

    pdf_file = images2pdf(album_path)
    # print(pdf_file)

    await send_comic(bot, event.group_id, pdf_file, f'本{album.album_id}')


key3 = on_fullmatch('热门本子', priority=10)


@key3.handle()
async def _(bot: Bot, event: Event):
    pass

# async def job(gid: int):
# 	bot = nonebot.get_bot()
#
# 	comic_folder = get_comic_favorite()
# 	print(f"随机本子: {comic_folder}")
# 	zip_file = get_comic_zip(comic_folder)
#
# 	await send_comic(bot, gid, zip_file)
# 	await bot.send_group_msg(group_id=gid, message="每日一本正经人严选")
#
#
# plugin_config = [
# 		[839497425, Time(20, 00)],  # 正人君子
# ]
# for gid, t in plugin_config:
# 	print(f"添加定时任务: {gid} {t}")
# 	scheduler.add_job(job, "cron", hour=t.hour, minute=t.minute, id=str(time.time_ns()), args=[gid])
