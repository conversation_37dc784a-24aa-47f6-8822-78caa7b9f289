import io
import re
import time
from random import choice

import httpx
import requests
from PIL import Image, ImageDraw, ImageFont

from nonebot.adapters.onebot.v11 import Bot, Event, MessageSegment
from nonebot.adapters.onebot.v11.message import Message
from nonebot.plugin import PluginMetadata
from nonebot.plugin.on import on_fullmatch

__plugin_meta__ = PluginMetadata(
		name="舔狗日记",
		description="别再舔了xd",
		usage="发送 舔狗日记 即可触发小作文",
		type="application",
)

max_char_per_line = 28  # 每行最多显示的字符数


def add_text_to_image(image_path, text1, text2):
	# 打开图片
	img = Image.open(image_path)
	width, height = img.size

	# 载入字体
	try:
		font1 = ImageFont.truetype("/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc", 50)  # 中文字体
		font2 = ImageFont.truetype("/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc", 40)  # 日记文本字号
	except IOError:
		font1 = ImageFont.load_default()
		font2 = ImageFont.load_default()

	# 创建一个新的图像来扩展原图
	new_height = height + 300  # 扩展图片的高度
	new_img = Image.new('RGB', (width, new_height), (255, 255, 255))  # 白色背景
	new_img.paste(img, (0, 0))  # 将原图粘贴到新图上

	draw = ImageDraw.Draw(new_img)

	# 第一段文本：居中对齐
	bbox1 = draw.textbbox((0, 0), text1, font=font1)  # 获取文本边界框
	text1_width = bbox1[2] - bbox1[0]  # 计算文本宽度
	text1_height = bbox1[3] - bbox1[1]  # 计算文本高度
	x1 = (width - text1_width) // 2  # 居中计算
	y1 = height + 20  # 距离原图的底部20像素
	draw.text((x1, y1), text1, font=font1, fill=(0, 0, 0))  # 黑色文字

	# 第二段文本：按字符数换行并每行开头空两个字符
	def wrap_text_by_chars(text, max_chars):
		""" 根据最大字符数拆分文本 """
		lines = []
		if len(text) > max_chars - 2:
			lines.append(text[:max_chars - 2])
			text = text[max_chars - 2:]
		else:
			lines.append(text)
			return lines
		while len(text) > max_chars:
			lines.append(text[:max_chars])
			text = text[max_chars:]
		if text:
			lines.append(text)
		return lines

	# 设置文本显示的位置
	text2_lines = wrap_text_by_chars(text2, max_char_per_line)  # 根据字符数拆分文本
	y2 = y1 + text1_height + 10  # 第二段文本的位置
	first = True
	x2 = 0
	line_height = 0
	for line in text2_lines:
		if first:
			bbox2 = draw.textbbox((0, 0), line, font=font2)  # 获取文本边界框
			line_width = bbox2[2] - bbox2[0]  # 计算文本宽度
			line_height = bbox2[3] - bbox2[1]  # 计算文本高度
			x2 = (width - line_width) // 2  # 居中计算
			first = False
			draw.text((x2 + 47, y2), line, font=font2, fill=(0, 0, 0))  # 黑色文字
		else:
			draw.text((x2, y2), line, font=font2, fill=(0, 0, 0))  # 黑色文字
		y2 += line_height + 5  # 每行之间有5像素的间隔

	# 保存新的图片
	byte_io = io.BytesIO()
	new_img.save(byte_io, format="PNG")
	byte_io.seek(0)  # 将文件指针移动到开头
	return byte_io.getvalue()  # 返回字节数据


async def dog():
	# with open(r'src/plugins/舔狗日记/dog.txt', 'r', encoding='GBK') as f:
	# 	text = choice(f.read().splitlines())
	# 	f.close()
	# Time = re.findall(r'tm_year=(\d*), tm_mon=(\d*), tm_mday=(\d*),', str(time.localtime()))[0]
	# weather_list = (
	# '小雨', '中雨', '大雨', '晴', '小雪', '中雪', '大雪', '暴雪', '雷阵雨', '冰雹', '阴', '大风', '雨夹雪', '晴转多云')
	# weather = choice(weather_list)
	async with httpx.AsyncClient() as client:
		response = await client.get("https://cloud.qqshabi.cn/api/tiangou/api.php")
	response = response.text
	date = response[4:11].replace('日', '日  ')
	text: str = response[12:]
	if text.startswith("\"") and text.endswith("\""):
		text = text[1:-1]
	# 示例用法
	image_path = "src/plugins/舔狗日记/img.png"  # 输入的图片路径
	with open(image_path, "rb") as f:
		image_bytes = f.read()
		return Message(date + MessageSegment.image(image_bytes) + text)


# img_bytes =(add_text_to_image(image_path, date, text))
# return MessageSegment.image(img_bytes)


key = on_fullmatch('舔狗日记', priority=50)


@key.handle()
async def key_handle(bot: Bot, event: Event):
	# print("舔狗命令")
	msg = await dog()
	await key.finish(msg)
