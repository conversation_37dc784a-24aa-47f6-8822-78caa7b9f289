import asyncio
import time
import re
from enum import Enum
from pathlib import Path
from typing import Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime
import os

from playwright.async_api import async_playwright, Page, Browser
import httpx
from nonebot.log import logger


class Resolution(Enum):
    """分辨率枚举类"""
    RESOLUTION_360 = "360"
    RESOLUTION_540 = "540"

    @classmethod
    def from_string(cls, value: str) -> 'Resolution':
        """从字符串获取分辨率枚举"""
        if value == "360":
            return cls.RESOLUTION_360
        elif value == "540":
            return cls.RESOLUTION_540
        else:
            raise ValueError(f"未知分辨率: {value}")


@dataclass
class VideoGenerationConfig:
    """视频生成配置类"""
    file_path: Path
    prompt_text: str
    duration: str = "6"
    motion_amplitude: str = "1.3"
    resolution: Resolution = Resolution.RESOLUTION_540
    seed: str = "-1"
    # 默认是当前文件的目录
    download_dir: Path = Path(__file__).parent.parent.joinpath('downloads')
    download_name: str = ""

    def __post_init__(self):
        # 检查图片是否存在
        if not isinstance(self.file_path, Path) or not self.file_path.exists():
            raise FileNotFoundError(f"指定的图片文件不存在: {self.file_path}")

        # 检查关键词是否超过200个
        if len(self.prompt_text.split()) > 200:
            raise ValueError("提示词不能超过200个单词")

        # 检查时长是否位于3-6秒之间
        if not (3 <= int(self.duration) <= 6):
            raise ValueError("视频时长必须在3到6秒之间")

        # 检查运动幅度是否在0.5-4之间
        if not (0.5 <= float(self.motion_amplitude) <= 4):
            raise ValueError("运动幅度必须在0.5到4之间")
        # 检查分辨率是否为360或540
        if self.resolution not in (Resolution.RESOLUTION_360, Resolution.RESOLUTION_540):
            raise ValueError("分辨率必须为360或540")
        # 检查随机种子是否为数字
        if not self.seed.isdigit() and self.seed != "-1":
            raise ValueError("随机种子必须为数字")

        # 检查下载目录是否存在
        if not isinstance(self.download_dir, Path):
            raise ValueError("下载目录必须是Path对象")
        self.download_dir.mkdir(parents=True, exist_ok=True)


class AnisoraVideoDownloader:
    """Anisora视频下载工具类"""

    def __init__(self, config: VideoGenerationConfig):
        self.config = config
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None

        self.url = "https://bilibili-index-anisora.ms.show"
        # 页面选择器配置
        self.selectors = {
            "file_input": "#component-5 > div.image-container.svelte-1hdlew6 > div.upload-container.svelte-1hdlew6.reduced-height > button > input[type=file]",
            "prompt_input": "#component-6 > label > div > textarea",
            "duration_input": "#component-8 > div.wrap.svelte-10lj3xl > div.head.svelte-10lj3xl > div > input",
            "motion_input": "#component-9 > div.wrap.svelte-10lj3xl > div.head.svelte-10lj3xl > div > input",
            "resolution_360": "#component-10 > div.wrap.svelte-1kzox3m > label:nth-child(1)",
            "resolution_540": "#component-10 > div.wrap.svelte-1kzox3m > label:nth-child(2)",
            "seed_input": "#component-11 > label > input",
            "generate_button": "#component-13",
            "queue_time": "#component-16 > div.wrap.default.full.svelte-ls20lj > div.progress-text.svelte-ls20lj.meta-text",
            "download_url": "#component-18 > div.file-preview-holder.svelte-1rvzbk6 > table > tbody > tr > td.download.svelte-1rvzbk6 > a",
            "error_popup": "body > gradio-app > div > main > div.toast-wrap.svelte-pu0yf1 > div > div",
            "upload_file": "#component-5 > div.image-container.svelte-1hdlew6 > div.upload-container.svelte-1hdlew6.reduced-height > div > img"
        }

        self.download_id = time.strftime("%Y%m%d%H%M%S", time.localtime())

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._init_browser()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        # 关闭浏览器
        if self.browser:
            await self.browser.close()

    async def _init_browser(self):
        """初始化浏览器"""
        from .browser_utils import BrowserUtils

        self.playwright = await async_playwright().__aenter__()
        self.browser = await self.playwright.chromium.launch(
            headless=True,
            args=BrowserUtils.get_browser_args()
        )
        self.page = await self.browser.new_page()
        self.page.set_default_timeout(120000)
        await self.page.set_viewport_size({"width": 2560, "height": 1440})

        # 设置headless浏览器增强功能
        await BrowserUtils.setup_headless_browser(self.page)

    async def generate_video(self):
        """完整的视频生成和下载流程"""
        # 导航到anisora页面
        logger.info(f"正在访问页面: {self.url}")
        await self.page.goto(self.url, wait_until="networkidle", timeout=120000)

        await self._screen_shot()

        # """上传文件"""
        file_input = await self.page.query_selector(self.selectors["file_input"])
        if not file_input:
            raise Exception("文件输入框未找到")
        await file_input.set_input_files(self.config.file_path)
        logger.info(f"已上传文件: {self.config.file_path}")

        await self._screen_shot()

        # 设置提示词
        prompt_input = await self.page.query_selector(self.selectors["prompt_input"])
        if not prompt_input:
            raise Exception("提示词输入框未找到")
        await prompt_input.fill(self.config.prompt_text)
        logger.info(f"已输入提示词: {self.config.prompt_text}")

        # 设置视频时长
        duration_input = await self.page.query_selector(self.selectors["duration_input"])
        if not duration_input:
            raise Exception("时长输入框未找到")
        await duration_input.fill(self.config.duration)
        logger.info(f"已设置生成时长: {self.config.duration} 秒")

        # 设置运动幅度
        motion_input = await self.page.query_selector(self.selectors["motion_input"])
        if not motion_input:
            raise Exception("运动幅度输入框未找到")
        await motion_input.fill(self.config.motion_amplitude)
        logger.info(f"已设置运动幅度: {self.config.motion_amplitude}")

        # 设置分辨率
        if self.config.resolution == Resolution.RESOLUTION_360.value:
            resolution_element = await self.page.query_selector(self.selectors["resolution_360"])
        else:
            resolution_element = await self.page.query_selector(self.selectors["resolution_540"])
        if not resolution_element:
            raise Exception(f"分辨率选择器未找到: {self.config.resolution}p")
        await resolution_element.click()
        logger.info(f"设置分辨率为{self.config.resolution}p")

        # 设置随机种子
        seed_input = await self.page.query_selector(self.selectors["seed_input"])
        if not seed_input:
            raise Exception("随机种子输入框未找到")
        await seed_input.fill(self.config.seed)
        logger.info(f"已设置随机种子: {self.config.seed}")

        await self.page.wait_for_timeout(5000)
        await self._screen_shot()

        # 检查是否有报错弹窗提示
        error_popup = await self.page.query_selector(self.selectors["error_popup"])
        if error_popup:
            error_message = await error_popup.text_content()
            raise Exception(f"操作失败，错误信息: {error_message}")

        # 检查是否存在上传的图片
        try:
            uploaded_image = await self.page.wait_for_selector(self.selectors["upload_file"], timeout=60000)
        except Exception as e:
            raise Exception("上传的图片失败，可能是图片违规或者网络错误")
        finally:
            await self._screen_shot()

        await asyncio.sleep(10)
        # 点击生成按钮
        generate_button = await self.page.query_selector(self.selectors["generate_button"])
        if not generate_button:
            raise Exception("生成按钮未找到")
        await generate_button.click()
        logger.info("已点击生成按钮")

        # 将页面滚动到顶部
        await self.page.evaluate("window.scrollTo(0, 0)")

        await self._screen_shot()

        # 等待操作完成
        await self.page.wait_for_timeout(2000)

    async def _screen_shot(self, name: str = None):
        name = name or datetime.now().strftime('%Y%m%d_%H%M%S')
        # 截图页面，用于分析
        screenshot_path = Path(
            self.config.download_dir) / f"screenshots/{self.download_id}/screenshot_{name}.png"
        await self.page.screenshot(path=screenshot_path)

    async def download_video(self):
        """下载生成的视频"""
        await self._screen_shot("download")
        # 等待视频生成完成
        queue_time_selector = self.selectors["queue_time"]
        queue_pattern = re.compile(
            r"queue: (?P<current_position>\d+)/(?P<queue_length>\d+) \| (?P<waited_time>\d+\.\d)/(?P<estimated_time>\d+\.\d)s")

        queue_time = await self.page.query_selector(queue_time_selector)
        if queue_time:
            queue_time_text = await queue_time.text_content()
            logger.debug(f"队列生成时间: {queue_time_text}")

            queue_match = queue_pattern.search(queue_time_text)
            if queue_match:
                current_position = queue_match.group("current_position")
                queue_length = queue_match.group("queue_length")
                waited_time = queue_match.group("waited_time")
                estimated_time = queue_match.group("estimated_time")
                return {
                    "done": False,
                    "message": f"当前队列位置: {current_position}/{queue_length}, 预计等待时间:{waited_time}/{estimated_time}s"
                }
            else:
                return {
                    "done": False,
                    "message": f"无法解析队列时间信息：{queue_time_text}"
                }

        # 使用URL下载
        download_url_element = await self.page.query_selector(self.selectors["download_url"])
        if not download_url_element:
            raise Exception("下载链接元素未找到")

        download_url = await download_url_element.get_attribute("href")
        logger.info(f"下载链接: {download_url}")

        if not self.config.download_name:
            file_name = Path(download_url).name
        else:
            file_name = self.config.download_name
        download_path = Path(self.config.download_dir) / file_name

        if download_path.exists():
            return {
                "done": True,
                "message": download_path
            }

        # 使用httpx下载文件
        async with httpx.AsyncClient() as client:
            response = await client.get(download_url)
            if response.status_code == 200:
                with open(download_path, 'wb') as f:
                    f.write(response.content)
                logger.info(f"文件已下载到: {download_path}")
            else:
                raise Exception(f"下载失败，状态码: {response.status_code}")

        return {
            "done": True,
            "message": download_path
        }


async def main():
    """
    主函数 - 使用示例
    """
    # 配置参数
    config = VideoGenerationConfig(
        file_path=Path("../images/tFD-Gley-Dia-00000.jpg"),
        prompt_text="脱掉上衣，赤裸上身，露出胸部",
        duration="5",
        motion_amplitude="2",
        resolution=Resolution.RESOLUTION_540,
    )

    # 使用工具类
    async with AnisoraVideoDownloader(config) as downloader:
        try:
            await downloader.generate_video()

            logger.info("视频生成任务已提交，等待下载...")
            while True:
                result = await downloader.download_video()
                if result["done"]:
                    logger.info(f"下载完成: {result['message']}")
                    break
                else:
                    logger.info(result["message"])
                    await asyncio.sleep(10)
        except Exception as e:
            logger.error(f"任务失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
