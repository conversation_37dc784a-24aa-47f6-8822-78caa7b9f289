import asyncio
from typing import Optional, Union
import httpx
from nonebot.log import logger


class DrawingService:
    def __init__(self):
        self.url = "https://cephalon.cloud/user-center/v1/model/comfyui"
        self.headers = {
            "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJJbmZvIjoiYXBpOjE5NDA5NDY2MzQ3MjY5NjUyNDgiLCJleHAiOjE3ODMxMzAxOTd9.aUvGuZxOtTj11uVDv_MFE00JwCb2ylFudYKeatWgUlk",
            "Model-Id": "1854732937730371541",
            "Content-Type": "application/json"
        }

    async def generate_image(
        self,
        prompt: str,
        width: int = 1024,
        height: int = 1024,
        guidance_scale: float = 3.5,
        steps: int = 25,
        seed: Optional[int] = None
    ) -> Union[bytes, str]:
        """
        生成图片

        Args:
            prompt: 绘图提示词
            width: 图片宽度
            height: 图片高度
            guidance_scale: 引导比例
            steps: 生成步数
            seed: 随机种子

        Returns:
            图片字节数据或错误信息
        """
        payload = {
            "prompt": prompt,
            "width": width,
            "height": height,
            "guidance_scale": guidance_scale,
            "steps": steps
        }

        if seed is not None:
            payload["seed"] = seed

        logger.info(f"开始调用绘图API - 提示词: {prompt[:50]}{'...' if len(prompt) > 50 else ''}")
        logger.debug(f"绘图参数 - 尺寸: {width}x{height}, 步数: {steps}, 引导比例: {guidance_scale}")

        try:
            async with httpx.AsyncClient(timeout=300) as client:
                logger.debug(f"发送绘图请求到: {self.url}")
                response = await client.post(self.url, headers=self.headers, json=payload)

                if response.status_code == 200:
                    # API直接返回二进制图片数据
                    image_size = len(response.content)
                    logger.info(f"绘图成功 - 图片大小: {image_size} bytes")
                    return response.content
                else:
                    logger.error(f"绘图API请求失败 - 状态码: {response.status_code}, 响应: {response.text[:200]}")
                    return f"绘图请求失败: {response.status_code} - {response.text}"

        except httpx.TimeoutException:
            logger.error("绘图API请求超时")
            return "绘图请求超时，请稍后重试"
        except Exception as e:
            logger.error(f"绘图服务异常: {str(e)}")
            return f"绘图服务出错: {str(e)}"


# 全局绘图服务实例
drawing_service = DrawingService()
