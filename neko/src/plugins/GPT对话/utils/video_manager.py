"""
视频管理器
统一管理视频生成、处理和上传功能
"""

import asyncio
import time
import re
from pathlib import Path
from typing import Dict, Any, Optional
from nonebot.log import logger
import nonebot
from nonebot.adapters.onebot.v11 import Bot, Event, MessageSegment
from .redis_manager import redis_manager, TaskStatus
from .img2video import AnisoraVideoDownloader, VideoGenerationConfig, Resolution


class VideoManager:
    """视频管理器，统一处理视频生成相关功能"""

    def __init__(self):
        self.processing_tasks = set()  # 正在处理的任务ID集合
        self.max_concurrent_tasks = 2  # 最大并发任务数

        # 创建视频保存目录
        self.video_dir = Path("/media/movies/nsfw/anisora")
        self.video_dir.mkdir(exist_ok=True)

        logger.info(f"视频管理器已初始化，视频保存目录: {self.video_dir}")

    # ==================== MCP工具接口 ====================

    async def generate_video(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """生成视频MCP工具"""
        try:
            # 获取必要参数
            image_path = arguments.get("image_path")
            prompt = arguments.get("prompt", "")
            user_id = arguments.get("user_id")  # 真实的QQ号
            user_name = arguments.get("user_name")  # 用户昵称
            group_id = arguments.get("group_id")

            # 可选参数
            duration = arguments.get("duration", "6")
            motion_amplitude = arguments.get("motion_amplitude", "1.3")
            resolution = arguments.get("resolution", "540")

            # 验证必要参数
            if not all([image_path, user_id, group_id]):
                return {"success": False, "error": "缺少必要参数"}

            # 验证图片文件是否存在
            image_path = Path(image_path)
            if not image_path.exists():
                return {"success": False, "error": f"图片文件不存在: {image_path}"}

            logger.info(f"开始创建视频生成任务 - 用户: {user_id}({user_name}), 图片: {image_path}")

            # 创建任务
            result = await redis_manager.create_task(
                user_id=user_id,
                user_name=user_name,
                group_id=group_id,
                image_path=image_path,
                prompt=prompt,
                duration=duration,
                motion_amplitude=motion_amplitude,
                resolution=resolution
            )

            if result["success"]:
                logger.info(f"视频生成任务创建成功 - 用户: {user_id}, 任务ID: {result['task_id']}")
                return {"success": True, "message": result["message"], "task_id": result["task_id"]}
            else:
                return result

        except Exception as e:
            error_msg = self._format_error_message(str(e))
            logger.error(f"视频生成工具执行失败: {error_msg}")
            return {"success": False, "error": error_msg}

    async def check_video_status(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """检查视频生成状态MCP工具"""
        try:
            user_id = arguments.get("user_id")
            if not user_id:
                return {"success": False, "error": "缺少必要参数: user_id"}

            return await redis_manager.get_task_status(user_id)

        except Exception as e:
            error_msg = self._format_error_message(str(e))
            logger.error(f"查询视频状态失败: {error_msg}")
            return {"success": False, "error": error_msg}

    async def cancel_video_task(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """取消视频生成任务MCP工具"""
        try:
            user_id = arguments.get("user_id")
            if not user_id:
                return {"success": False, "error": "缺少必要参数: user_id"}

            logger.info(f"取消视频生成任务 - 用户: {user_id}")
            return await redis_manager.cancel_task(user_id)

        except Exception as e:
            error_msg = self._format_error_message(str(e))
            logger.error(f"取消视频任务失败: {error_msg}")
            return {"success": False, "error": error_msg}

    def _format_error_message(self, error: str) -> str:
        """格式化错误信息，提供用户友好的错误描述"""
        error_lower = error.lower()

        if "timeout" in error_lower or "超时" in error_lower:
            return "操作超时，请稍后重试"
        elif "network" in error_lower or "网络" in error_lower:
            return "网络连接异常，请检查网络后重试"
        elif "file not found" in error_lower or "文件不存在" in error_lower:
            return "文件处理失败，请重新上传图片"
        elif "permission" in error_lower or "权限" in error_lower:
            return "系统权限不足，请联系管理员"
        elif "memory" in error_lower or "内存" in error_lower:
            return "系统资源不足，请稍后重试"
        elif "queue" in error_lower or "队列" in error_lower:
            return "服务队列繁忙，请稍后重试"
        else:
            # 保留原始错误信息，但简化显示
            return f"处理失败: {error[:100]}{'...' if len(error) > 100 else ''}"

    # ==================== 后台处理 ====================

    async def process_pending_tasks(self):
        """处理所有待处理的任务"""
        try:
            pending_tasks = await redis_manager.get_all_pending_tasks()

            if not pending_tasks:
                return

            # logger.info(f"发现 {len(pending_tasks)} 个待处理的视频任务")

            # 过滤出真正需要处理的任务
            tasks_to_process = []
            for task in pending_tasks:
                task_id = task['task_id']
                status = TaskStatus(task['status'])

                if task_id in self.processing_tasks:
                    continue

                if status == TaskStatus.PENDING:
                    tasks_to_process.append(task)
                elif status == TaskStatus.PROCESSING:
                    asyncio.create_task(self._check_task_progress(task))

            # 限制并发处理数量
            available_slots = self.max_concurrent_tasks - len(self.processing_tasks)
            if available_slots > 0:
                tasks_to_start = tasks_to_process[:available_slots]
                for task in tasks_to_start:
                    asyncio.create_task(self._process_single_task(task))

        except Exception as e:
            logger.error(f"处理待处理任务时出错: {str(e)}")

    async def _process_single_task(self, task: dict):
        """处理单个任务"""
        task_id = task['task_id']

        try:
            self.processing_tasks.add(task_id)
            await redis_manager.update_task_status(task_id, TaskStatus.PROCESSING)

            logger.info(f"开始处理视频任务: {task_id}")

            # 创建视频生成配置
            config = VideoGenerationConfig(
                file_path=Path(task['image_path']),
                prompt_text=task['prompt'],
                duration=task['duration'],
                motion_amplitude=task['motion_amplitude'],
                resolution=Resolution.from_string(task['resolution']),
                seed="-1",
                download_dir=self.video_dir,
                download_name=f"video_{task_id}_{int(time.time())}.mp4"
            )

            # 执行视频生成
            try:
                async with (AnisoraVideoDownloader(config) as downloader):
                    # 提交生成任务
                    try:
                        await downloader.generate_video()
                        logger.info(f"任务 {task_id} 已提交到生成队列")
                    except Exception as e:
                        raise Exception(f"提交视频生成任务失败: {str(e)}")

                    # 立即获取一次预计生成时间并通知用户
                    try:
                        initial_result = await downloader.download_video()
                        if not initial_result["done"]:
                            # 发送预计时间通知
                            await self._send_initial_notification(task, initial_result["message"])
                            logger.info(f"任务 {task_id} 预计时间已通知用户: {initial_result['message']}")
                    except Exception as e:
                        logger.warning(f"获取任务 {task_id} 预计时间失败: {str(e)}")

                    # 等待生成完成
                    while True:
                        try:
                            result = await downloader.download_video()
                        except Exception as e:
                            raise Exception(f"检查视频生成进度失败: {str(e)}")
                    # result = {
                    #     "done": True,
                    #     "message": Path(self.video_dir) / f"video_b1e0b428-6296-4a02-a1ca-79fe5a8fdd25_1752028418.mp4"
                    # }

                        if result["done"]:
                            # 生成完成，保存到本地
                            video_path = result["message"]
                            logger.info(f"任务 {task_id} 生成完成: {video_path}")

                            # 验证文件是否存在
                            if not Path(video_path).exists():
                                raise Exception(f"生成的视频文件不存在: {video_path}")

                            # 更新任务状态为完成
                            await redis_manager.update_task_status(
                                task_id,
                                TaskStatus.COMPLETED,
                                result_path=str(video_path)
                            )

                            # 发送完成通知并上传文件
                            await self._send_completion_notification(task, video_path)
                            break
                    else:
                        # 更新进度信息并打印日志
                        progress_info = self._parse_progress_message(result["message"])
                        await redis_manager.update_task_status(
                            task_id,
                            TaskStatus.PROCESSING,
                            progress=progress_info
                        )
                        # 打印进度日志
                        # 当前队列位置: 14/20, 预计等待时间:522.6/2311.1s
                        logger.info(f"📹 任务 {task_id[:8]}... 进度: {result['message']}")

                        # 等待剩下时间的一半
                        wait_time = \
                            int(
                                abs(
                                    eval(
                                        (re.search(r"[\d/.]+(?=s)", result['message']).group()).replace('/', '-')
                                    )
                                ) / 2
                            )
                        # 取max,避免等待时间过短
                        await asyncio.sleep(max(wait_time, 5))
            except Exception as video_error:
                # 视频生成过程中的具体错误
                raise Exception(f"视频生成过程失败: {str(video_error)}")

        except Exception as e:
            logger.error(f"处理任务 {task_id} 时出错: {str(e)}")
            await redis_manager.update_task_status(
                task_id,
                TaskStatus.FAILED,
                error_message=str(e)
            )

            # 通知用户任务失败
            await self._send_failure_notification(task, str(e))
        finally:
            self.processing_tasks.discard(task_id)

    async def _check_task_progress(self, task: dict):
        """检查处理中任务的进度"""
        pass

    def _parse_progress_message(self, message: str) -> dict:
        """解析进度消息"""
        progress = {"current_position": 0, "queue_length": 0, "waited_time": 0.0, "estimated_time": 0.0}

        try:
            # 提取队列位置信息
            queue_match = re.search(r'(\d+)/(\d+)', message)
            if queue_match:
                progress["current_position"] = int(queue_match.group(1))
                progress["queue_length"] = int(queue_match.group(2))

            # 提取时间信息
            time_match = re.search(r'(\d+\.?\d*)/(\d+\.?\d*)s', message)
            if time_match:
                progress["waited_time"] = float(time_match.group(1))
                progress["estimated_time"] = float(time_match.group(2))

        except Exception as e:
            pass

        return progress

    async def _upload_video_to_group(self, video_path: Path, group_id: str, task_id: str):
        """上传视频文件到群聊"""
        try:
            # 获取bot实例并上传文件
            bot = nonebot.get_bot()
            # await bot.upload_group_file(
            #     group_id=int(group_id),
            #     file=video_path.as_uri(),
            #     name=f'AI生成视频_{task_id[:8]}.mp4'
            # )
            await bot.send_group_msg(message=MessageSegment.video(video_path), group_id=group_id)

            logger.info(f"视频已上传到群聊 {group_id}: {video_path.name}")
            return True

        except Exception as e:
            logger.error(f"上传视频文件到群聊失败: {str(e)}")
            return False

    async def _send_initial_notification(self, task: dict, progress_message: str):
        """发送任务初始预计时间通知"""
        try:
            group_id = task['group_id']
            user_id = task['user_id']
            user_name = task.get('user_name', f"用户{user_id}")
            task_id = task['task_id']

            logger.info(f"📹 发送预计时间通知 - 群组: {group_id}, 用户: {user_id}({user_name}), 任务: {task_id}")

            # 发送预计时间消息（使用CQ码@用户）
            bot = nonebot.get_bot()
            await bot.send_group_msg(
                group_id=int(group_id),
                message=f"[CQ:at,qq={user_id}] 🎬 视频生成任务已提交！\n📊 {progress_message}"
            )

        except Exception as e:
            logger.error(f"发送初始通知失败: {str(e)}")

    async def _send_completion_notification(self, task: dict, video_path: Path):
        """发送任务完成通知并上传视频文件"""
        try:
            group_id = task['group_id']
            user_id = task['user_id']
            user_name = task.get('user_name', f"用户{user_id}")
            task_id = task['task_id']

            logger.info(f"📹 任务完成 - 群组: {group_id}, 用户: {user_id}({user_name}), 任务: {task_id}")

            # 发送完成消息
            bot = nonebot.get_bot()
            # await bot.send_group_msg(
            #     group_id=int(group_id),
            #     message=f"🎬 视频生成完成！正在上传文件..."
            # )

            # 上传视频文件
            upload_success = await self._upload_video_to_group(video_path, group_id, task_id)
            if upload_success:
                await bot.send_group_msg(
                    group_id=int(group_id),
                    message=f"[CQ:at,qq={user_id}] ✅ 视频文件上传成功！"
                )
            else:
                await bot.send_group_msg(
                    group_id=int(group_id),
                    message=f"[CQ:at,qq={user_id}] ❌ 视频文件上传失败，但文件已保存在服务器本地"
                )

        except Exception as e:
            logger.error(f"发送完成通知失败: {str(e)}")

    async def _send_failure_notification(self, task: dict, error_message: str):
        """发送任务失败通知"""
        try:
            group_id = task['group_id']
            user_id = task['user_id']
            user_name = task.get('user_name', f"用户{user_id}")
            task_id = task['task_id']

            logger.error(f"📹 任务失败 - 群组: {group_id}, 用户: {user_id}({user_name}), 任务: {task_id}, 错误: {error_message}")

            # 发送失败消息（使用CQ码@用户）
            bot = nonebot.get_bot()
            await bot.send_group_msg(
                group_id=int(group_id),
                message=f"[CQ:at,qq={user_id}] ❌ 视频生成失败：{error_message}"
            )

        except Exception as e:
            logger.error(f"发送失败通知失败: {str(e)}")


# 全局实例
video_manager = VideoManager()
