"""
Redis管理器
统一管理Redis连接、上下文存储和任务管理
"""

import json
import time
import uuid
import os
from typing import Dict, Any, Optional, List
from enum import Enum
from pathlib import Path
import redis.asyncio as redis
from nonebot.log import logger


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class RedisManager:
    """Redis管理器，统一处理上下文和任务管理"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.context_expire_time = 300  # 5分钟过期时间
        self.task_expire_time = 3600  # 任务1小时后过期
        self._init_redis()
    
    def _init_redis(self):
        """初始化Redis连接"""
        try:
            redis_host = os.getenv('REDIS_HOST', '***********')
            redis_port = int(os.getenv('REDIS_PORT', '6389'))
            redis_db = int(os.getenv('REDIS_DB', '0'))
            redis_password = os.getenv('REDIS_PASSWORD', None)
            
            if redis_password == '':
                redis_password = None
            
            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                db=redis_db,
                password=redis_password,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            
            logger.info(f"Redis连接初始化成功 - {redis_host}:{redis_port}/{redis_db}")
            
        except Exception as e:
            logger.error(f"Redis连接初始化失败: {str(e)}")
            self.redis_client = None
    
    async def test_connection(self) -> bool:
        """测试Redis连接"""
        if not self.redis_client:
            return False
        
        try:
            await self.redis_client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis连接测试失败: {str(e)}")
            return False
    
    # ==================== 上下文管理 ====================
    
    def _get_context_key(self, group_id: str) -> str:
        """获取群聊上下文的Redis键"""
        return f"gpt_context:{group_id}"
    
    async def get_context_messages(self, group_id: str) -> List[Dict]:
        """获取群聊的上下文消息历史"""
        if not self.redis_client:
            return []
        
        try:
            key = self._get_context_key(group_id)
            context_data = await self.redis_client.get(key)
            
            if context_data:
                context = json.loads(context_data)
                messages = context.get('messages', [])
                return messages
            else:
                return []
                
        except Exception as e:
            logger.error(f"获取上下文失败 - 群组: {group_id}, 错误: {str(e)}")
            return []
    
    async def add_context_message(self, group_id: str, user_name: str, user_input: str, ai_response: str):
        """添加新的上下文消息"""
        if not self.redis_client:
            return
        
        try:
            key = self._get_context_key(group_id)
            
            existing_context = await self.redis_client.get(key)
            if existing_context:
                context = json.loads(existing_context)
                messages = context.get('messages', [])
            else:
                messages = []
            
            messages.append({"role": "user", "content": f"名字为'{user_name}'的用户对你说：{user_input}"})
            messages.append({"role": "assistant", "content": ai_response})
            
            # 限制上下文长度
            if len(messages) > 20:
                messages = messages[-20:]
            
            context_data = {
                'messages': messages,
                'last_update': int(time.time())
            }
            
            await self.redis_client.setex(
                key,
                self.context_expire_time,
                json.dumps(context_data, ensure_ascii=False)
            )
            
        except Exception as e:
            logger.error(f"保存上下文失败 - 群组: {group_id}, 错误: {str(e)}")
    
    async def clear_context(self, group_id: str):
        """清除群聊的上下文"""
        if not self.redis_client:
            return
        
        try:
            key = self._get_context_key(group_id)
            await self.redis_client.delete(key)
            logger.info(f"清除群聊 {group_id} 上下文成功")
        except Exception as e:
            logger.error(f"清除上下文失败 - 群组: {group_id}, 错误: {str(e)}")
    
    async def extend_context_expire(self, group_id: str):
        """延长上下文过期时间"""
        if not self.redis_client:
            return
        
        try:
            key = self._get_context_key(group_id)
            await self.redis_client.expire(key, self.context_expire_time)
        except Exception as e:
            logger.error(f"延长上下文过期时间失败 - 群组: {group_id}, 错误: {str(e)}")
    
    # ==================== 任务管理 ====================
    
    def _get_user_task_key(self, user_id: str) -> str:
        """获取用户任务的Redis键"""
        return f"video_task:user:{user_id}"
    
    def _get_task_detail_key(self, task_id: str) -> str:
        """获取任务详情的Redis键"""
        return f"video_task:detail:{task_id}"
    
    async def has_active_task(self, user_id: str) -> bool:
        """检查用户是否有活跃的任务"""
        if not self.redis_client:
            return False
        
        try:
            task_id = await self.redis_client.get(self._get_user_task_key(user_id))
            if not task_id:
                return False
            
            task_data = await self.redis_client.get(self._get_task_detail_key(task_id))
            if not task_data:
                await self.redis_client.delete(self._get_user_task_key(user_id))
                return False
            
            task = json.loads(task_data)
            status = TaskStatus(task.get('status', TaskStatus.PENDING.value))
            
            return status in [TaskStatus.PENDING, TaskStatus.PROCESSING]
            
        except Exception as e:
            logger.error(f"检查用户任务状态失败 - 用户: {user_id}, 错误: {str(e)}")
            return False
    
    async def create_task(self, user_id: str, user_name: str, group_id: str, image_path: Path, prompt: str,
                         duration: str = "6", motion_amplitude: str = "1.3",
                         resolution: str = "540") -> Dict[str, Any]:
        """创建新的视频生成任务"""
        if not self.redis_client:
            return {"success": False, "error": "Redis连接不可用"}
        
        try:
            if await self.has_active_task(user_id):
                return {"success": False, "error": "您已有正在进行的视频生成任务，请等待完成后再创建新任务"}
            
            task_id = str(uuid.uuid4())
            
            task_data = {
                "task_id": task_id,
                "user_id": user_id,
                "user_name": user_name,
                "group_id": group_id,
                "image_path": str(image_path),
                "prompt": prompt,
                "duration": duration,
                "motion_amplitude": motion_amplitude,
                "resolution": resolution,
                "status": TaskStatus.PENDING.value,
                "created_at": int(time.time()),
                "updated_at": int(time.time()),
                "error_message": None,
                "result_path": None,
                "progress": {"current_position": 0, "queue_length": 0, "waited_time": 0, "estimated_time": 0}
            }
            
            await self.redis_client.setex(
                self._get_task_detail_key(task_id),
                self.task_expire_time,
                json.dumps(task_data, ensure_ascii=False)
            )
            
            await self.redis_client.setex(
                self._get_user_task_key(user_id),
                self.task_expire_time,
                task_id
            )
            
            logger.info(f"创建视频生成任务成功 - {task_data}")
            
            return {"success": True, "task_id": task_id, "message": f"视频生成任务已创建，任务ID: {task_id[:8]}..."}
            
        except Exception as e:
            logger.error(f"创建视频任务失败 - 用户: {user_id}, 错误: {str(e)}")
            return {"success": False, "error": f"创建任务失败: {str(e)}"}
    
    async def get_task_status(self, user_id: str) -> Dict[str, Any]:
        """获取用户的任务状态"""
        if not self.redis_client:
            return {"success": False, "error": "Redis连接不可用"}
        
        try:
            task_id = await self.redis_client.get(self._get_user_task_key(user_id))
            if not task_id:
                return {"success": False, "error": "您当前没有进行中的任务"}
            
            task_data = await self.redis_client.get(self._get_task_detail_key(task_id))
            if not task_data:
                return {"success": False, "error": "任务数据不存在"}
            
            task = json.loads(task_data)
            status = TaskStatus(task.get('status', TaskStatus.PENDING.value))
            
            result = {"success": True, "task_id": task_id, "status": status.value}
            
            if status == TaskStatus.PENDING:
                result["message"] = "📋 任务已创建，等待系统处理..."
            elif status == TaskStatus.PROCESSING:
                progress = task.get('progress', {})
                if progress and progress.get('current_position', 0) > 0:
                    current_pos = progress.get('current_position', 0)
                    queue_length = progress.get('queue_length', 0)
                    waited_time = progress.get('waited_time', 0.0)
                    estimated_time = progress.get('estimated_time', 0.0)
                    result["message"] = f"📹 视频生成中\n🔢 队列位置: {current_pos}/{queue_length}\n⏱️ 已等待: {waited_time:.1f}s\n⏰ 预计总时长: {estimated_time:.1f}s"
                    result["progress"] = progress
                else:
                    result["message"] = "📹 任务已提交，正在排队等待处理..."
            elif status == TaskStatus.COMPLETED:
                result["message"] = "✅ 视频生成完成！文件已上传到群聊"
                result["result_path"] = task.get('result_path')
            elif status == TaskStatus.FAILED:
                result["message"] = f"❌ 视频生成失败: {task.get('error_message', '未知错误')}"
            elif status == TaskStatus.CANCELLED:
                result["message"] = "🚫 任务已取消"
            
            return result
            
        except Exception as e:
            logger.error(f"获取任务状态失败 - 用户: {user_id}, 错误: {str(e)}")
            return {"success": False, "error": f"获取任务状态失败: {str(e)}"}
    
    async def update_task_status(self, task_id: str, status: TaskStatus, 
                               error_message: str = None, result_path: str = None,
                               progress: Dict[str, Any] = None) -> bool:
        """更新任务状态"""
        if not self.redis_client:
            return False
        
        try:
            task_data = await self.redis_client.get(self._get_task_detail_key(task_id))
            if not task_data:
                return False
            
            task = json.loads(task_data)
            task['status'] = status.value
            task['updated_at'] = int(time.time())
            
            if error_message:
                task['error_message'] = error_message
            if result_path:
                task['result_path'] = result_path
            if progress:
                task['progress'].update(progress)
            
            await self.redis_client.setex(
                self._get_task_detail_key(task_id),
                self.task_expire_time,
                json.dumps(task, ensure_ascii=False)
            )
            
            return True
            
        except Exception as e:
            logger.error(f"更新任务状态失败 - 任务ID: {task_id}, 错误: {str(e)}")
            return False
    
    async def get_all_pending_tasks(self) -> list:
        """获取所有待处理的任务"""
        if not self.redis_client:
            return []
        
        try:
            keys = await self.redis_client.keys("video_task:detail:*")
            pending_tasks = []
            
            for key in keys:
                task_data = await self.redis_client.get(key)
                if task_data:
                    task = json.loads(task_data)
                    status = TaskStatus(task.get('status', TaskStatus.PENDING.value))
                    if status in [TaskStatus.PENDING, TaskStatus.PROCESSING]:
                        pending_tasks.append(task)
            
            return pending_tasks
            
        except Exception as e:
            logger.error(f"获取待处理任务失败: {str(e)}")
            return []
    
    async def cancel_task(self, user_id: str) -> Dict[str, Any]:
        """取消用户的任务"""
        if not self.redis_client:
            return {"success": False, "error": "Redis连接不可用"}
        
        try:
            task_id = await self.redis_client.get(self._get_user_task_key(user_id))
            if not task_id:
                return {"success": False, "error": "您当前没有进行中的任务"}
            
            success = await self.update_task_status(task_id, TaskStatus.CANCELLED)
            
            if success:
                await self.redis_client.delete(self._get_user_task_key(user_id))
                logger.info(f"任务已取消 - 用户: {user_id}, 任务ID: {task_id}")
                return {"success": True, "message": "任务已取消"}
            else:
                return {"success": False, "error": "取消任务失败"}
                
        except Exception as e:
            logger.error(f"取消任务失败 - 用户: {user_id}, 错误: {str(e)}")
            return {"success": False, "error": f"取消任务失败: {str(e)}"}
    
    async def close(self):
        """关闭Redis连接"""
        if self.redis_client:
            await self.redis_client.close()


# 全局实例
redis_manager = RedisManager()
