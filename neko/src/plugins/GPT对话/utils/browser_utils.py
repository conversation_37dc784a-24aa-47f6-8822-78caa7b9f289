"""
浏览器工具类
提供headless模式下的增强元素定位功能
"""

from playwright.async_api import Page
from nonebot.log import logger


class BrowserUtils:
    """浏览器工具类，提供增强的元素定位功能"""
    
    @staticmethod
    async def setup_headless_browser(page: Page):
        """设置headless浏览器的增强功能"""
        
        # 添加JavaScript来隐藏headless特征
        await page.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
            
            window.chrome = {
                runtime: {},
            };
        """)
    
    @staticmethod
    async def wait_for_element(page: Page, selector: str, timeout: int = 30000):
        """等待元素出现，增强版"""
        try:
            # 首先等待元素出现
            await page.wait_for_selector(selector, timeout=timeout)
            
            # 等待元素可见
            await page.wait_for_selector(selector, state="visible", timeout=timeout)
            
            # 额外等待一下确保元素完全加载
            await page.wait_for_timeout(1000)
            
            return await page.query_selector(selector)
        except Exception as e:
            logger.warning(f"等待元素失败 {selector}: {str(e)}")
            # 尝试滚动页面，有时元素在视口外
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await page.wait_for_timeout(2000)
            
            # 再次尝试
            try:
                await page.wait_for_selector(selector, timeout=10000)
                return await page.query_selector(selector)
            except:
                logger.error(f"最终等待元素失败: {selector}")
                return None
    
    @staticmethod
    def get_browser_args():
        """获取headless模式的浏览器启动参数"""
        return [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
        ]
