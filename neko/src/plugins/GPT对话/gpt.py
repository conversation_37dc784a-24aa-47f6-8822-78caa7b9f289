import json
from typing import Union, Tuple, Optional, List, Dict
from openai import AsyncOpenAI
from nonebot.log import logger

from .config import config
from .mcp_tools.drawing_tool import drawing_tool_server
from .mcp_tools.video_tool import video_tool_server
from .utils.redis_manager import redis_manager

client = AsyncOpenAI(api_key=config.api_key, base_url=config.api_url)


async def req(content: str, group_id: str = None, user_id: str = None, user_name: str = None, image_path: str = None) -> Union[str, Tuple[str, bytes]]:
    """
    处理用户请求，支持文本回复、图片生成和视频生成，支持上下文对话

    Args:
        content: 用户输入内容
        group_id: 群聊ID，用于上下文管理
        user_id: 用户QQ号，用于任务管理和通知
        user_name: 用户名称，用于上下文记录
        image_path: 图片路径，用于视频生成

    Returns:
        str: 纯文本回复
        Tuple[str, bytes]: (文本回复, 图片数据)
    """
    # 构建基础消息
    messages = [
        {"role": "system", "content": config.default_system}
    ]

    # 如果提供了群组ID，尝试获取上下文
    if group_id:
        try:
            context_messages = await redis_manager.get_context_messages(group_id)
            if context_messages:
                messages.extend(context_messages)
                await redis_manager.extend_context_expire(group_id)
        except Exception as e:
            logger.error(f"获取上下文失败: {str(e)}")

    # 添加当前用户消息
    # content已经包含了用户名信息，不需要重复添加
    messages.append({"role": "user", "content": content})

    # 构建工具列表
    tools = []

    # # 根据输入类型选择工具
    # if image_path:
    #     # 用户发送了图片，提供视频生成工具
    #     logger.info("用户发送了图片，提供视频生成工具")
    tools.append({
        "type": "function",
        "function": {
            "name": "generate_video",
            "description": "根据用户提供的图片和需求生成视频。你需要将用户的描述转换为详细、专业的视频生成提示词。",
            "parameters": {
                "type": "object",
                "properties": {
                    "prompt": {
                        "type": "string",
                        "description": "经过你优化和完善的视频生成提示词。请将用户的简单描述转换为详细的英文提示词，描述图片中的元素应该如何运动、变化。包含具体的动作描述、运动方向、速度感、氛围等。例如：用户说'让她走向我'，你应该转换为类似'woman walking towards camera, smooth movement, natural gait, gentle expression, soft lighting'这样的专业提示词。"
                    },
                    "duration": {
                        "type": "string",
                        "description": "视频时长秒数，3-6之间",
                        "default": "6"
                    },
                    "motion_amplitude": {
                        "type": "string",
                        "description": "运动幅度，0.5-4之间",
                        "default": "1.3"
                    },
                    "resolution": {
                        "type": "string",
                        "description": "视频分辨率，360或540",
                        "default": "540"
                    }
                },
                "required": ["prompt"]
            }
        }
    })
    # else:
    #     # 用户只发送了文本，提供绘图工具
    #     logger.info("用户仅发送文本，提供绘图工具")
    tools.append({
        "type": "function",
        "function": {
            "name": "generate_image",
            "description": "根据用户需求生成图片。你需要将用户的描述转换为详细、专业的图片生成提示词。",
            "parameters": {
                "type": "object",
                "properties": {
                    "prompt": {
                        "type": "string",
                        "description": "经过你优化和完善的图片生成提示词。请将用户的简单描述转换为详细的英文提示词，包含具体的视觉元素、风格、构图、光影、色彩等细节描述。例如：用户说'画个美女'，你应该转换为类似'beautiful woman, detailed face, elegant pose, soft lighting, high quality, photorealistic'这样的专业提示词。"
                    },
                    "width": {
                        "type": "integer",
                        "description": "图片宽度像素",
                        "default": 1024
                    },
                    "height": {
                        "type": "integer",
                        "description": "图片高度像素",
                        "default": 1024
                    }
                },
                "required": ["prompt"]
            }
        }
    })

    # 视频状态查询和取消工具始终可用（不依赖图片输入）
    tools.extend([
        {
            "type": "function",
            "function": {
                "name": "check_video_status",
                "description": "当用户询问视频生成进度、状态、完成情况、任务进展、视频制作进度等相关问题时，必须调用此工具来查询用户的视频任务状态。关键词包括：进度、状态、完成、任务、视频生成、制作等。",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "cancel_video_task",
                "description": "当用户要求取消、停止、终止、中断视频生成任务时，必须调用此工具。关键词包括：取消、停止、终止、中断、不要了等。",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        }
    ])

    # 如果没有工具，设置为None
    if not tools:
        tools = None

    # 构建请求参数
    request_params = {
        "messages": messages,
        "tools": tools,
        "tool_choice": "auto" if tools else None,
        "stream": False
    }

    # 只有当模型名称不为空时才添加model参数
    if config.model_name:
        request_params["model"] = config.model_name

    logger.info(f"GPT请求参数: {request_params}")

    response = await client.chat.completions.create(**request_params)

    logger.info(f"GPT响应: {response}")

    message = response.choices[0].message

    # 检查是否有工具调用
    if message.tool_calls:
        tool_call = message.tool_calls[0]
        logger.info(f"调用工具: {tool_call.function.name}")

        if tool_call.function.name == "generate_image":
            try:
                # 处理arguments为None的情况
                if tool_call.function.arguments:
                    args = json.loads(tool_call.function.arguments)
                else:
                    args = {}
                result = await drawing_tool_server.call_tool("generate_image", args)

                if result.get("success"):
                    success_message = result.get("message", "图片生成成功")
                    logger.info(f"绘图成功，图片大小: {len(result['image_data'])} bytes")

                    # 保存上下文
                    if group_id and user_name:
                        try:
                            await redis_manager.add_context_message(
                                group_id=group_id,
                                user_name=user_name,
                                user_input=content,
                                ai_response=f"[生成图片] {success_message}"
                            )
                        except Exception as e:
                            logger.error(f"保存上下文失败: {str(e)}")

                    return success_message, result["image_data"]
                else:
                    error_message = f"绘图失败: {result.get('error', '未知错误')}"
                    logger.error(error_message)

                    # 保存上下文
                    if group_id and user_name:
                        try:
                            await redis_manager.add_context_message(
                                group_id=group_id,
                                user_name=user_name,
                                user_input=content,
                                ai_response=error_message
                            )
                        except Exception as e:
                            logger.error(f"保存上下文失败: {str(e)}")

                    return error_message

            except Exception as e:
                error_message = f"绘图过程中出现错误: {str(e)}"
                logger.error(error_message)

                # 保存上下文
                if group_id and user_name:
                    try:
                        await redis_manager.add_context_message(
                            group_id=group_id,
                            user_name=user_name,
                            user_input=content,
                            ai_response=error_message
                        )
                    except Exception as ctx_e:
                        logger.error(f"保存上下文失败: {str(ctx_e)}")

                return error_message

        elif tool_call.function.name in ["generate_video", "check_video_status", "cancel_video_task"]:
            try:
                # 处理arguments为None的情况
                if tool_call.function.arguments:
                    args = json.loads(tool_call.function.arguments)
                else:
                    args = {}

                # 为工具添加必要的上下文信息
                if tool_call.function.name == "generate_video":
                    args["image_path"] = str(image_path) if image_path else None
                    args["user_id"] = user_id  # 真实的QQ号
                    args["user_name"] = user_name  # 用户昵称
                    args["group_id"] = group_id
                elif tool_call.function.name in ["check_video_status", "cancel_video_task"]:
                    args["user_id"] = user_id  # 使用真实的QQ号作为任务标识

                result = await video_tool_server.call_tool(tool_call.function.name, args)

                if result.get("success"):
                    success_message = result.get("message", "操作成功")

                    # 保存上下文
                    if group_id and user_name:
                        try:
                            await redis_manager.add_context_message(
                                group_id=group_id,
                                user_name=user_name,
                                user_input=content,
                                ai_response=f"[视频工具] {success_message}"
                            )
                        except Exception as e:
                            logger.error(f"保存上下文失败: {str(e)}")

                    return success_message
                else:
                    error_message = f"视频工具执行失败: {result.get('error', '未知错误')}"
                    logger.error(error_message)

                    # 保存上下文
                    if group_id and user_name:
                        try:
                            await redis_manager.add_context_message(
                                group_id=group_id,
                                user_name=user_name,
                                user_input=content,
                                ai_response=error_message
                            )
                        except Exception as e:
                            logger.error(f"保存上下文失败: {str(e)}")

                    return error_message

            except Exception as e:
                error_message = f"视频工具执行过程中出现错误: {str(e)}"
                logger.error(error_message)

                # 保存上下文
                if group_id and user_name:
                    try:
                        await redis_manager.add_context_message(
                            group_id=group_id,
                            user_name=user_name,
                            user_input=content,
                            ai_response=error_message
                        )
                    except Exception as ctx_e:
                        logger.error(f"保存上下文失败: {str(ctx_e)}")

                return error_message

    # 返回普通文本回复
    response_text = message.content or "抱歉，我没有理解您的问题。"

    # 保存上下文
    if group_id and user_name:
        try:
            await redis_manager.add_context_message(
                group_id=group_id,
                user_name=user_name,
                user_input=content,
                ai_response=response_text
            )
        except Exception as e:
            logger.error(f"保存上下文失败: {str(e)}")

    return response_text
