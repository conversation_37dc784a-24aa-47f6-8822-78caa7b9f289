"""
视频生成工具MCP服务器
"""

from typing import Dict, Any
from nonebot.log import logger
from ..utils.video_manager import video_manager


class VideoToolServer:
    """视频生成工具服务器"""

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用视频工具"""
        logger.debug(f"调用视频工具: {tool_name}, 参数: {arguments}")

        if tool_name == "generate_video":
            return await video_manager.generate_video(arguments)
        elif tool_name == "check_video_status":
            return await video_manager.check_video_status(arguments)
        elif tool_name == "cancel_video_task":
            return await video_manager.cancel_video_task(arguments)
        else:
            return {"success": False, "error": f"未知的工具名称: {tool_name}"}


video_tool_server = VideoToolServer()
