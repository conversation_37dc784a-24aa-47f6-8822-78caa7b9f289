"""
绘图工具MCP服务器
"""

from typing import Dict, Any
from nonebot.log import logger
from ..utils.drawing import drawing_service


class DrawingToolServer:
    """绘图工具服务器"""

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用绘图工具"""
        if tool_name != "generate_image":
            return {"success": False, "error": f"未知的工具名称: {tool_name}"}

        try:
            prompt = arguments.get("prompt", "")
            width = arguments.get("width", 1024)
            height = arguments.get("height", 1024)

            if not prompt:
                return {"success": False, "error": "缺少必要参数: prompt"}

            logger.info(f"开始生成图片 - 提示词: {prompt}")

            image_data = await drawing_service.generate_image(
                prompt=prompt, width=width, height=height
            )

            if isinstance(image_data, bytes):
                logger.info(f"图片生成成功 - 尺寸: {width}x{height}, 大小: {len(image_data)} bytes")
                return {
                    "success": True,
                    "image_data": image_data,
                    "message": f"成功生成图片 - 尺寸: {width}x{height}"
                }
            else:
                logger.error(f"图片生成失败: {image_data}")
                return {"success": False, "error": str(image_data)}

        except Exception as e:
            logger.error(f"绘图工具执行失败: {str(e)}")
            return {"success": False, "error": f"绘图工具执行失败: {str(e)}"}


drawing_tool_server = DrawingToolServer()
