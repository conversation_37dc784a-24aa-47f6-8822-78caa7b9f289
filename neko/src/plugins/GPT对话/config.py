import json
import os
from nonebot.log import logger

config_path = os.path.realpath(__file__).replace(r'config.py', '') + "setting.json"


class Config:
	def __init__(self):
		self.api_url: str = ""
		self.api_key: str = ""
		self.default_system: str = ""
		self.model_name: str = ""

		self.init()

	def init(self):
		try:
			with open(config_path, "r", encoding="utf-8") as file:
				data = json.load(file)
			logger.info(f"GPT配置文件加载成功: {config_path}")
		except FileNotFoundError:
			logger.error(f"GPT配置文件未找到：{config_path}")
			return
		except json.JSONDecodeError as e:
			logger.error(f"GPT配置文件JSON格式错误：{e}")
			return

		self.api_url = data.get("api_url", "")
		self.api_key = data.get("api_key", "")
		self.default_system = data.get("default_system", "")
		self.model_name = data.get("model_name", "")

		logger.info(f"GPT配置加载完成 - API URL: {self.api_url}, 模型名称: {self.model_name or '未指定'}")
		if self.api_key:
			logger.debug(f"API Key已配置: {self.api_key[:10]}...{self.api_key[-4:]}")
		else:
			logger.warning("API Key未配置")


config = Config()
