import asyncio
import json
from pathlib import Path
from typing import Union, List, Any
import httpx
import nonebot
import requests
from nonebot.adapters.onebot.v11 import (
    <PERSON><PERSON>,
    GroupMessageEvent,
    MessageEvent
)
from nonebot.plugin.on import on_message, on_command
from nonebot.rule import to_me
from nonebot.log import logger

from .config import config
from .gpt import req
from .utils.redis_manager import redis_manager
from .utils.video_manager import video_manager

try:
    from nonebot.adapters.onebot.v11 import Message, MessageSegment  # type: ignore
except ImportError:
    from nonebot.adapters.cqhttp import Message, MessageSegment  # type: ignore
from nonebot.plugin import PluginMetadata

# 导入定时任务
try:
    from nonebot_plugin_apscheduler import scheduler
    SCHEDULER_AVAILABLE = True
except ImportError:
    logger.warning("nonebot_plugin_apscheduler未安装，视频处理定时任务将被禁用")
    SCHEDULER_AVAILABLE = False

__plugin_meta__ = PluginMetadata(
    name="GPT对话",
    description="简化的GPT聊天插件",
    usage="@机器人 + 消息内容即可开始对话",
    type='application',
    extra={
        "author": "wzz",
    },
)

# 插件启动时的日志
logger.info("GPT对话插件已加载")
if config.api_key:
    logger.info(f"API配置: {config.api_url}")
else:
    logger.warning("GPT API Key未配置，插件可能无法正常工作")


# 测试Redis连接
async def test_redis_on_startup():
    """插件启动时测试Redis连接"""
    if await redis_manager.test_connection():
        logger.info("Redis功能已启用")
    else:
        logger.warning("Redis连接失败，相关功能将被禁用")


# 在插件加载时异步测试Redis连接
import asyncio

try:
    asyncio.create_task(test_redis_on_startup())
except Exception as e:
    logger.error(f"Redis连接测试失败: {str(e)}")

# 添加视频处理定时任务
if SCHEDULER_AVAILABLE:
    @scheduler.scheduled_job("interval", seconds=20, id="video_task_processor")
    async def process_video_tasks():
        """每20秒检查一次视频任务并处理"""
        try:
            await video_manager.process_pending_tasks()
        except Exception as e:
            logger.error(f"视频任务处理出错: {str(e)}")

    logger.info("视频处理定时任务已启动，每20秒检查一次")
else:
    logger.warning("定时任务功能不可用，视频任务需要手动处理")


def get_message(data: json) -> dict[str, Any]:
    """
    说明：
        获取消息中的信息
    参数：
        :param data: event.json()
    """

    def get_text_img(message: List[dict]):
        """
        说明：
            获取文本和图片的内容
        """
        t = []
        i = []

        for item in message:
            if item['type'] == 'text':
                t.append(item['data']['text'])
            elif item['type'] == 'image':
                i.append([item['data']['file'], item['data']['url']])

        return t, i

    # 用户昵称
    nickname = data['sender']['nickname'] if 'sender' in data else "未知用户"
    text = ""
    img = []
    # 处理可能的回复消息
    if data['reply'] is not None:
        text += f"根据用户`{data['reply']['sender']['nickname']}`的消息："
        _t, _i = get_text_img(data['reply']['message'])
        text += ",".join(_t)
        text += "\n"
        img.extend(_i)

    # 获取消息主体的内容
    main_t, main_i = get_text_img(data['message'])
    text += f"用户`{nickname}`说: "
    text += ",".join(main_t)
    img.extend(main_i)

    if len(img) > 1:
        raise ValueError("暂时不支持多张图片")
    elif len(img) == 0:
        img = None
    else:
        img = img[0]
        img_name = img[0]
        img_url = img[1]

        # 确保images目录存在
        images_dir = Path("/media/movies/nsfw/anisora") / "images"
        images_dir.mkdir(exist_ok=True)

        img_path = images_dir / img_name
        with open(img_path, "wb") as f:
            f.write(requests.get(img_url).content)

        img = img_path

    # 拼接消息
    result = {
        "text": text,
        "img": img
    }
    return result


def getNode(message: Message):
    return MessageSegment.node_custom(
        user_id=2244759730,
        nickname="高性能萝卜子",
        content=message
    )


key = on_message(rule=to_me(), priority=1, block=True)


@key.handle()
async def _(bot: Bot, event: MessageEvent):
    # 只允许群聊
    if not isinstance(event, GroupMessageEvent):
        logger.debug(f"拒绝私聊请求，用户ID: {event.user_id}")
        await key.finish("为了安全，暂不支持私聊哦///")



    try:
        msg = get_message(json.loads(event.json()))
    except ValueError as e:
        logger.error(f"处理消息失败: {str(e)}")
        await key.finish("❌ 暂不支持多张图片，请发送单张图片")

    group_id = str(event.group_id)
    user_id = str(event.user_id)

    # 获取用户名称
    user_name = event.sender.nickname if hasattr(event.sender, 'nickname') else f"用户{user_id}"

    logger.info(f"GPT请求 - 群组: {group_id}, 用户: {user_name}, 消息长度: {len(msg['text'])}")
    if msg['img']:
        logger.info(f"包含图片: {msg['img']}")

    # try:
    response = await req(msg['text'], group_id=group_id, user_id=user_id, user_name=user_name, image_path=msg['img'])

    # 检查是否返回了图片
    if isinstance(response, tuple):
        text_response, image_data = response
        logger.info(f"生成图片 - 群组: {group_id}, 图片大小: {len(image_data)} bytes")
        # 发送文本回复
        if text_response:
            await key.send(text_response)
        # 发送图片
        try:
            await key.send(MessageSegment.image(image_data))
        except Exception as e:
            await key.send("发送失败，正在重试...")
            await asyncio.sleep(3)
            await key.send(MessageSegment.image(image_data))
    else:
        # 纯文本回复
        if len(response) > 500:
            await nonebot.get_bot().send_group_forward_msg(group_id=group_id, messages=[
                getNode(response)
            ])
            await key.send("消息过长，已转发")
        else:
            await key.send(response)
    # except Exception as e:
    #     logger.error(f"GPT请求失败 - 群组: {group_id}, 错误: {str(e)}")


# 清除上下文命令
clear_context = on_command("清除上下文", aliases={"清空上下文", "重置对话"}, priority=1, block=True)


@clear_context.handle()
async def _(bot: Bot, event: MessageEvent):
    # 只允许群聊
    if not isinstance(event, GroupMessageEvent):
        await clear_context.finish("此命令只能在群聊中使用")

    group_id = str(event.group_id)
    user_id = str(event.user_id)

    try:
        await redis_manager.clear_context(group_id)
        logger.info(f"用户 {user_id} 清除了群聊 {group_id} 的上下文")
        await clear_context.finish("✅ 已清除本群的对话上下文")
    except Exception as e:
        logger.error(f"清除上下文失败 - 群组: {group_id}, 用户: {user_id}, 错误: {str(e)}")
        await clear_context.finish("❌ 清除上下文失败，请稍后重试")


# 注意：视频任务查询和取消功能已集成到MCP工具中
# 用户可以通过@机器人的方式使用这些功能
