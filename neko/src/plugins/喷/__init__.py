import os
import re
import time
from random import choice

import nonebot
from nonebot.adapters.onebot.v11 import Bo<PERSON>, Event
from nonebot.adapters.onebot.v11.message import Message
from nonebot.plugin import on_command, PluginMetadata

__plugin_meta__ = PluginMetadata(
		name="喷",
		description="不可以说藏话！",
		usage="喷+@目标对象",
		type='application',
		extra={
				"author"       : "wzz",
				"menu_data"    : [
						{
								"func"             : "轻喷",
								"trigger_method"   : "喷+@目标对象",
								"trigger_condition": "开头匹配[Any]",
								"brief_des"        : "浅浅说一句藏话",
								"detail_des"       : "无",
						},
						{
								"func"             : "火力全开",
								"trigger_method"   : "使劲喷+@目标对象",
								"trigger_condition": "开头匹配[Any]",
								"brief_des"        : "不建议使用，内置cd",
								"detail_des"       : "无",
						},
				],
				"menu_template": "default",
		},
)


def pen(type: int) -> str:
	file_path = os.path.realpath(__file__).replace(r'__init__.py', '') + "pen/{}.txt".format(type)
	with open(file_path, 'r', encoding='GBK') as f:
		data = choice(f.read().splitlines())
		f.close()

	return data


def getUser(message: str) -> str:
	# try:
	print(message)
	QQ = re.findall(r'at:qq=(\d*)', message)[0]
	# atQQ = f"[CQ:at,qq={QQ}]"
	# except:
	#     QQ = re.findall(r"喷(.*)\"", message)[0]
	#     atQQ = f"@{QQ} "
	return QQ


# [message.group.normal]: Message -177904240 from 2539907983@[群:974270687] "喷<le>[CQ:at,qq=1417684364]</le> "

key = on_command('喷', priority=50, block=True)

global_config = nonebot.get_driver().config.superusers
@key.handle()
async def key_handle(bot: Bot, event: Event):
	atQQ = getUser(str(event))
	if not atQQ:
		key.finish()
	if atQQ == "2244759730":
		await key.finish("怎么会有人自己骂自己？小憨憨")

	event_user = event.get_user_id()

	for super_user in global_config:
		if atQQ == super_user:
			await key.finish(message=Message(f"[CQ:at,qq={event_user}] 我才不会说主人坏话呢！"))
	await key.finish(message=Message(f"[CQ:at,qq={atQQ}] {pen(1)}"))


key = on_command('使劲喷', priority=5, block=True)
match_data = {}
@key.handle()
async def key_handle(bot: Bot, event: Event):
	# print(match_data)
	# print(str(event))
	atQQ = getUser(str(event))
	if atQQ == "2244759730":
		await key.finish("怎么会有人自己骂自己？小憨憨")
	event_user = event.get_user_id()
	for super_user in global_config:
		if atQQ == super_user:
			await key.finish(message=Message(f"[CQ:at,qq={event_user}]我才不会说主人坏话呢！"))
	if event_user in match_data.keys():
		if int(time.time()) - match_data[event_user] < 60:
			await key.finish("冷却中...")
		else:
			match_data[event_user] = int(time.time())
	else:
		match_data[event_user] = int(time.time())
	await key.finish(message=Message(f"[CQ:at,qq={atQQ}] {pen(0)}"))
	# await key.finish(str(int(time.time())))
