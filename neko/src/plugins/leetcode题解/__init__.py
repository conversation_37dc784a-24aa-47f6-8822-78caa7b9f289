import asyncio
import re
from nonebot.adapters.onebot.v11 import <PERSON><PERSON>, GroupMessageEvent, MessageSegment
from nonebot.adapters.onebot.v11.message import Message
from nonebot.plugin import PluginMetadata
from nonebot.plugin.on import on_regex
from nonebot.log import logger

from .solution_leetcode import Solution, analyze_code

__plugin_meta__ = PluginMetadata(
    name="leetcode题解",
    description="根据题号获取leetcode题解",
    usage="在指定群聊中发送纯数字即可获取对应题号的题解",
    type='application',
    extra={
        "author": "wzz",
        "menu_data": [
            {
                "func": "获取题解",
                "trigger_method": "发送纯数字",
                "trigger_condition": "正则匹配[群号:974270687]",
                "brief_des": "根据题号获取leetcode题解",
                "detail_des": "仅在群号974270687中启用，发送纯数字即可获取对应题号的Python题解",
            },
        ],
        "menu_template": "default",
    },
)

# 限制只在群号974270687中启用
TARGET_GROUP = 974270687

# 匹配纯数字消息（1-4位数字）
leetcode_matcher = on_regex(r'^\d{1,4}$', priority=50, block=True)


@leetcode_matcher.handle()
async def handle_leetcode_request(bot: Bot, event: GroupMessageEvent):
    # 检查是否在指定群聊中
    if event.group_id != TARGET_GROUP:
        return

    # 获取题号
    question_num = int(str(event.get_message()).strip())
    user_id = event.get_user_id()

    logger.info(f"收到leetcode题解请求 - 群组: {event.group_id}, 用户: {user_id}, 题号: {question_num}")

    # 发送处理中的消息
    await leetcode_matcher.send(f"正在获取第{question_num}题的题解，请稍候...")

    try:
        # 创建Solution实例并获取题解
        solution = Solution(question_num)
        codes = await solution.solution_leetcode()

        if not codes:
            await leetcode_matcher.finish(f"抱歉，未能找到第{question_num}题的Python题解")
            return

        # 使用AI分析代码并生成简化题解
        analyzed_solution = await analyze_code(codes)

        if analyzed_solution:
            # 构建回复消息
            reply_msg = f"第{question_num}题 Python题解：\n\n{analyzed_solution}"
            await leetcode_matcher.finish(Message(reply_msg))
        # else:
        #     await leetcode_matcher.finish(f"获取到代码但分析失败，第{question_num}题可能暂时无法处理")

    except Exception as e:
        logger.error(f"获取leetcode题解失败: {str(e)}")
        await leetcode_matcher.finish(f"获取第{question_num}题题解时出现错误，请稍后重试")