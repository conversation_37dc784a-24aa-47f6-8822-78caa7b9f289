import asyncio
import time
from openai import AsyncOpenAI
from playwright.async_api import async_playwright, <PERSON>
from nonebot.log import logger


class Solution:
    def __init__(self, question_num: int):
        self.page: Page = None
        self.question_num = str(question_num)
        self.host = 'https://leetcode.cn'

    async def solution_leetcode(self):
        """
        根据题号开始爬取解法
        :return:
        """
        try:
            # 启动浏览器
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                self.page = await browser.new_page()

                # 前往leetcode题库
                await self.page.goto("https://leetcode.cn/problemset/")
                await self.page.wait_for_load_state("load")
                await asyncio.sleep(3)

                # 搜索题目
                solutions_page_url = await self.search_question_by_num()
                if not solutions_page_url:
                    logger.error(f"未找到题号 {self.question_num} 的题目")
                    return []

                await self.page.goto(solutions_page_url)
                await self.page.wait_for_load_state("load")
                await asyncio.sleep(3)

                # 获取python解法
                solution_url = await self.get_python_solution()
                if not solution_url:
                    logger.error(f"未找到题号 {self.question_num} 的Python解法")
                    return []

                await self.page.goto(solution_url)
                await self.page.wait_for_load_state("load")
                await asyncio.sleep(3)

                # 获取代码
                code = await self.get_code()
                return code

        except Exception as e:
            logger.error(f"获取题解过程中出现异常: {str(e)}")
            return []

    async def search_question_by_num(self) -> str:
        """
        根据题号搜索题目
        :return: 题解URL
        """
        # 输入题号
        search_locator = self.page.get_by_placeholder('搜索题目')

        if await search_locator.count() == 0:
            logger.warning('没找到搜索框')
            return ''

        await search_locator.fill(self.question_num)
        await self.page.wait_for_load_state("load")

        # 获取题目URL
        question_locator = self.page.locator(f'#\\3{self.question_num[0]} {self.question_num[1:]}')
        question_url = await question_locator.get_attribute('href')
        logger.info(f"找到题目URL: {question_url}")
        return self.host + question_url + '/solutions/'

    async def get_python_solution(self):
        """
        获取python解法
        :return: 任一python解法URL
        """
        # 点击python解法筛选
        python_locator = self.page.locator('span[translate="no"]').filter(has_text='Python')
        if await python_locator.count() == 0:
            logger.warning('没找到python筛选')
            return ''
        logger.info('找到python筛选')
        await python_locator.nth(0).click()
        await asyncio.sleep(3)
        logger.info('python筛选点击成功')

        # 获取第一个python解法
        solution_locator = self.page.locator('a[href*="/solutions/"]')
        solution_url = await solution_locator.nth(0).get_attribute('href')
        return self.host + solution_url

    async def get_code(self):
        """
        获取代码
        :return: 代码
        """
        results = []
        main_container = self.page.locator('div.relative > div.break-words')
        # 定位包含复制按钮的父级容器
        container = main_container.locator('div.group.relative[translate="no"]')
        container_count = await container.count()
        if container_count == 0:
            other_container = main_container.locator('div.monaco-editor')
            for n in range(await other_container.count()):
                results.append(await other_container.nth(n).inner_text())
        elif container_count == 1:
            results.append(await container.inner_text())
        else:
            for n in range(await container.count()):
                results.append(await container.nth(n).inner_text())

        return results


# 调用豆包的GPT api来分析题解
async def analyze_code(codes: list[str]):
    if not codes:
        logger.warning("没有代码需要分析")
        return None

    try:
        config = {
            "api_url": "https://api.deepseek.com",
            "api_key": "***********************************",
            "default_system": "使用中文简要回答问题，返回题解需要的python代码即可，不需要任何多余的说明",
            "model_name": "deepseek-reasoner"
        }

        client = AsyncOpenAI(api_key=config["api_key"], base_url=config["api_url"])
        content = "我正在尝试使用python完成leetcode题目，现在有一些和题解相关的内容，我希望你整理完成之后，返回一个简易的python题解给我"
        for i, c in enumerate(codes):
            content += f"\n{i}, {c}"

        messages = [
            {"role": "system", "content": config["default_system"]},
            {"role": "user", "content": content}
        ]
        request_params = {
            "messages": messages,
            "stream": False,
            "model": config["model_name"]
        }
        response = await client.chat.completions.create(**request_params)

        message = response.choices[0].message.content

        logger.info(f"AI分析完成，返回题解长度: {len(message) if message else 0}")
        return message

    except Exception as e:
        logger.error(f"AI分析代码时出现异常: {str(e)}")
        return None


async def main():
    s = Solution(735)
    codes = await s.solution_leetcode()
    print(codes)
    await analyze_code(codes)


if __name__ == '__main__':
    asyncio.run(main())
