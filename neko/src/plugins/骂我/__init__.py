import json
import os
import random
import io
from typing import Optional

from minio import Minio
from nonebot.adapters.onebot.v11 import Bo<PERSON>, Event
from nonebot.adapters.onebot.v11.message import MessageSegment
from nonebot.plugin import PluginMetadata, get_plugin_config
from nonebot.plugin.on import on_fullmatch
from openai import BaseModel
from pydantic import Field

__plugin_meta__ = PluginMetadata(
		name="骂我",
		description="钉宫病犯了",
		usage="发送‘骂我’即可被奖励",
		type="application",
)
voice_text = [
		{
				"index"      : "01",
				"original"   : "アンタなんて豆腐の角に頭ぶつけて死んじゃえばいいのよ！",
				"copywriting": "你这家伙，找块豆腐一头撞死算了！"
		}
		,
		{
				"index"      : "02",
				"original"   : "いい加減、目覚まし時計で起きなさいよね！何でいつも私が起こさなきゃいけないのよ！！",
				"copywriting": "你还是老老实实定个闹钟起床啊！干吗每次都非要我来叫醒你嘛。"
		}
		,
		{
				"index"      : "03",
				"original"   : "うるさい、うるさい、うるさい！",
				"copywriting": "吵死了吵死了吵死了！真是吵死了！"
		}
		,
		{
				"index"      : "04",
				"original"   : "選んだのは私でアンタじゃないんだからね",
				"copywriting": "是我选的你，你可别以为是你给了我机会哦！"
		}
		,
		{
				"index"      : "05",
				"original"   : "おはよーじゃなくて、おはようございますでしょっ",
				"copywriting": "什么“早安”，应该说“您早上好”才对！"
		}
		,
		{
				"index"      : "06",
				"original"   : "勘違いしないでよね",
				"copywriting": "你可别胡思乱想"
		}
		,
		{
				"index"      : "07",
				"original"   : "別にあんたのためにやったわけじゃないんだから",
				"copywriting": "我才不是特地为你做的。"
		}
		,
		{
				"index"      : "08",
				"original"   : "キスなんて100年早いわよ！！",
				"copywriting": "接、接吻什么的，你还早了100年呢！"
		}
		,
		{
				"index"      : "09",
				"original"   : "くっ…胸のことを…",
				"copywriting": "哭…我的胸部…"
		}
		,
		{
				"index"      : "10",
				"original"   : "怪我でもされたら、私が困るじゃない",
				"copywriting": "要是你受伤的话，岂不是会让我很头疼。"
		}
		,
		{
				"index"      : "11",
				"original"   : "このぉバカ！",
				"copywriting": "你这个笨蛋！"
		}
		,
		{
				"index"      : "12",
				"original"   : "さよならなんて言ってあげないんだから！",
				"copywriting": "再见之类的话，我才懒得对你说…"
		}
		,
		{
				"index"      : "13",
				"original"   : "心配なんてしてないんだからね！",
				"copywriting": "我才没有为你担心！"
		}
		,
		{
				"index"      : "14",
				"original"   : "好きでこんな格好してるわけじゃないんだからね…あんたの前だけよ…",
				"copywriting": "我、我可不是因为自己喜欢才打扮成这样…只有在你面前罢了…"
		}
		,
		{
				"index"      : "15",
				"original"   : "もう…バカ…",
				"copywriting": "真是的…笨蛋…"
		}
		,
		{
				"index"      : "16",
				"original"   : "全力で来なさいよ？叩き潰してあげるから",
				"copywriting": "尽管放马过来吧！让我打你个落花流水！"
		}
		,
		{
				"index"      : "17",
				"original"   : "そばにいなさい！一生よ！いいわね！",
				"copywriting": "留在我身边！一辈子！没异议吧！"
		}
		,
		{
				"index"      : "18",
				"original"   : "誕生日プレゼントなんて誰にももらえないだろうから",
				"copywriting": "生日礼物这种东西，我想没有其他人会送给你了吧…"
		}
		,
		{
				"index"      : "19",
				"original"   : "はい",
				"copywriting": "hi~"
		}
		,
		{
				"index"      : "20",
				"original"   : "これプレゼント…",
				"copywriting": "这个送给你……"
		}
		,
		{
				"index"      : "21",
				"original"   : "ちょっと待ちなさいよ！この鈍感男！",
				"copywriting": "给我等一下！你这个木头人！"
		}
		,
		{
				"index"      : "22",
				"original"   : "なんで…",
				"copywriting": "为什么…"
		}
		,
		{
				"index"      : "23",
				"original"   : "なんで気付かないのよぉ！",
				"copywriting": "为什么你就是注意不到嘛！"
		}
		,
		{
				"index"      : "24",
				"original"   : "ついでだから、あんたも誘ってあげるわ",
				"copywriting": "正好顺便，我也邀请一下你吧。"
		}
		,
		{
				"index"      : "25",
				"original"   : "電話なんかしてこなくても…寂しくなんてないんだから…",
				"copywriting": "就算你不打电话给我，我也没什么寂寞的…"
		}
		,
		{
				"index"      : "26",
				"original"   : "隣に来ないでよ、恥ずかしいじゃない",
				"copywriting": "别离我这么近…丢死人了…"
		}
		,
		{
				"index"      : "27",
				"original"   : "な、なによあいつ！",
				"copywriting": "那、那家伙干什么嘛！"
		}
		,
		{
				"index"      : "28",
				"original"   : "私というものがありながら！うぅぅ",
				"copywriting": "明明已经有我了的说！呜呜…"
		}
		,
		{
				"index"      : "29",
				"original"   : "逃げないで、話くらいちゃんと聞きなさいよね",
				"copywriting": "别逃，至少听我把话说完！"
		}
		,
		{
				"index"      : "30",
				"original"   : "もうっ、知らない、バカ！！何言わせんのよ！！",
				"copywriting": "真是的，不管啦笨蛋！！你让人家怎么说嘛！！"
		}
		,
		{
				"index"      : "31",
				"original"   : "寝起きになにしてんのよ！",
				"copywriting": "你睡糊涂了吧，干什么呐！"
		}
		,
		{
				"index"      : "32",
				"original"   : "NOとは言わせないわよ！",
				"copywriting": "你可不许对我说“No”哦"
		}
		,
		{
				"index"      : "33",
				"original"   : "恥ずかしいけど、手、手くらい繋いであげてもいいわよ、ホラ！！",
				"copywriting": "虽然很丢人，让、让、让你牵牵手还是可以容忍…来吧…"
		}
		,
		{
				"index"      : "34",
				"original"   : "ひ…ひざまくらぁ！……い、一分だけだからねっ",
				"copywriting": "睡、睡…睡在我的腿上！？只、只能睡1分钟哦！"
		}
		,
		{
				"index"      : "35",
				"original"   : "ふ、二人しかいないんなら…",
				"copywriting": "唔，今天只有我们两个人的话…"
		}
		,
		{
				"index"      : "36",
				"original"   : "今日は仕方ないわね…",
				"copywriting": "真是拿你没办法呢…"
		}
		,
		{
				"index"      : "37",
				"original"   : "変態、変態、変態、変態、変態バカ大変態！！！",
				"copywriting": "变态、变态、变态、变态、变态笨蛋大变态！！！"
		}
		,
		{
				"index"      : "38",
				"original"   : "もう知らない…",
				"copywriting": "真是的，不管了啦…"
		}
		,
		{
				"index"      : "39",
				"original"   : "ボケーっとしてるんじゃないわよ！！",
				"copywriting": "别在那装傻了啦！！真是的"
		}
		,
		{
				"index"      : "40",
				"original"   : "蹴り飛ばすわよ！",
				"copywriting": "小心我一脚踢飞你！"
		}
		,
		{
				"index"      : "41",
				"original"   : "まっ…まぁ、アナタがどうしてもって言うのなら",
				"copywriting": "啊…也好，既然你坚持要带着…"
		}
		,
		{
				"index"      : "42",
				"original"   : "仕方ないけど…",
				"copywriting": "我也没办法了呢…"
		}
		,
		{
				"index"      : "43",
				"original"   : "見せたいわけじゃないんだから！",
				"copywriting": "所以说我不想给别人看到啊！"
		}
		,
		{
				"index"      : "44",
				"original"   : "あんただから…",
				"copywriting": "只想给你一个人…"
		}
		,
		{
				"index"      : "45",
				"original"   : "無視しないでよ！謝るから！",
				"copywriting": "不准无视我！快给我道歉！"
		}
		,
		{
				"index"      : "46",
				"original"   : "目から汗が出てるだけなんだから！",
				"copywriting": "所以说只是从眼睛里流出来的汗啦！"
		}
		,
		{
				"index"      : "47",
				"original"   : "涙じゃないわよぉーーー！",
				"copywriting": "才~不~是眼泪呢！"
		}
		,
		{
				"index"      : "48",
				"original"   : "もぉ～バカバカバカ！！",
				"copywriting": "真是的~笨蛋、笨蛋、笨蛋！！"
		}
		,
		{
				"index"      : "49",
				"original"   : "知らない知らない知らない",
				"copywriting": "不知道、不知道、不知道！"
		}
		,
		{
				"index"      : "50",
				"original"   : "妬いてるわけじゃないわよ、違うって言ってるじゃない！",
				"copywriting": "才没有吃醋呢，你在说什么傻话啊！"
		}
		,
		{
				"index"      : "51",
				"original"   : "優柔不断な奴はキライだからね！",
				"copywriting": "所以说我最讨厌优柔寡断的家伙了！"
		}
		,
		{
				"index"      : "52",
				"original"   : "よくないわよ。",
				"copywriting": "才不好呢。"
		}
		,
		{
				"index"      : "55",
				"original"   : "あたしのこと好きでも嫌いでもないなんて言うのは",
				"copywriting": "对你说我喜欢你还是不讨厌你什么的"
		}
		,
		{
				"index"      : "56",
				"original"   : "ラーメン伸びるわよ、はやく食べなさいよ！",
				"copywriting": "拉面都泡糟了啦！快点吃掉啦！"
		}
		,
		{
				"index"      : "57",
				"original"   : "えっふぅふぅしてくれ？ばかぁー",
				"copywriting": "诶，让我帮你吹吹？笨蛋啊你~"
		}
		,
		{
				"index"      : "58",
				"original"   : "利用するに値しないわね。アンタなんか…",
				"copywriting": "我才没有觉得你很有用呢…"
		}
		,
		{
				"index"      : "59",
				"original"   : "全然嬉しくないんだからっ",
				"copywriting": "我才没有很开心呢！"
		}
		,
		{
				"index"      : "60",
				"original"   : "連絡くらいよこしなさいよ！心配したんだから…",
				"copywriting": "要多多的和我联系！不然我会担心你的…"
		}
		,
		{
				"index"      : "61",
				"original"   : "ろくなことがないわね…あんたと一緒にいられるのは私くらいなんだから…",
				"copywriting": "所以说别做些不正经的事了…对好不容易才和你在一起的我…"
		}
		,
		{
				"index"      : "62",
				"original"   : "わ、私だけを見ていればいいのよ！",
				"copywriting": "只、只准你看着我哦！"
		}
]

key = on_fullmatch('骂我', priority=10)


class Config(BaseModel):
	minio_end_point: Optional[str] = Field(default=None)
	minio_access_key: Optional[str] = Field(default=None)
	minio_secret_key: Optional[str] = Field(default=None)


plugin_config: Config = get_plugin_config(Config)

minio_client = Minio(
		endpoint=plugin_config.minio_end_point,
		access_key=plugin_config.minio_access_key,
		secret_key=plugin_config.minio_secret_key,
		secure=False,
)


@key.handle()
async def _(bot: Bot, event: Event):
	d = random.choice(voice_text)
	objs = list(minio_client.list_objects('resource', prefix='dinggong-voices/' + d['index'], recursive=True))
	if len(objs) == 1:
		voice = minio_client.get_object('resource', objs[0].object_name)
		byte_io = voice.read()
		voice.close()
	
		await key.send(MessageSegment.record(byte_io))
		await key.finish(d['copywriting'])
