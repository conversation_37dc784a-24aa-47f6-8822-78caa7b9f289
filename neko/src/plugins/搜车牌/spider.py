import asyncio
import base64
import re
from urllib.parse import unquote

import httpx
import requests
from lxml import etree
# from nonebot import logger
from playwright.async_api import async_playwright, Page, Response

try:
    from .pojo import Magnet, MediaInfo
except ImportError:
    from pojo import Magnet, MediaInfo

headers = {
    'sec-ch-ua-platform': '"Linux"',
    'Referer': 'https://missav.live/dm15/ja',
    'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
    'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'Cookie': '''user_uuid=a115378c-a7b8-42e5-8b9e-85e2225c5a7f; _ga=GA1.1.1666713179.1747372231; search_history=[%22ipx-246%22]; _ga_JV54L39Q8H=GS2.1.s1747372230$o1$g1$t1747372249$j0$l0$h0; cf_clearance=nK2YMmAQ_O4QRE3MJ0XjcgWiJDuohzwGBZslq8LVgDE-1747372249-*******-ldH89PgtHzqfeXwrWS4ejioSR_QGiPkx0vV2Ty81eWVXLl_mrA8V6UuZi7s96Dmg9JEtY5CfoZUs4qLdYU3jHT2jqPGBYVIL8cugCh6P23_4KQPxOuSg_yRQ5CvaighAoerDqVcZYl9pjKrT1cp8EG5XSpGjPFexbjpAjgdU7QMXaGIn1PC3rx3L9lmPeeG2_v.6daM48c3Uj2MtIK9z.pJHfwvqGrs5NxvuFl66pPOt9mY6LI06SGriGGeuV0p7W3Qh9cVuMeAMhPNHf2GJnODMLwP4PIvVJ_14d7dkFnFkPZImrboNu7TBObzI1iT050Rjcd_i2aagYs.lwxhjj0XpQbrDGh5xj.hPGOfK8x0'''
}

proxy = "http://***********:7890"
transport = httpx.AsyncHTTPTransport(proxy=proxy)


async def getImageBy123av(mediaInfo: MediaInfo):
    name = mediaInfo.name
    mediaUrl = ""

    async def on_response(response: Response):
        print(response.request.resource_type, response.url)
        nonlocal mediaUrl
        if 'preview' in response.url and response.request.resource_type == 'image':
            mediaInfo.previewImg = await response.body()

        if mediaInfo.name.lower() not in response.url.lower():
            return

        if 'cover' in response.url and response.request.resource_type == 'image':
            mediaInfo.coverImg = await response.body()

        if response.request.resource_type == 'document' and 'search' in response.url:
            html = etree.HTML(await response.text())
            node = html.xpath('//*[@class="col-6 col-sm-4 col-lg-3"]')
            if not node:
                mediaUrl = None
                return
            node = node[0].xpath('./div/div[1]/a')[0]
            title = node.xpath('./@title')[0]
            if name.lower() not in title.lower():
                mediaUrl = None
                return
            mediaUrl = "https://123av.com/zh/" + node.xpath('./@href')[0]


    async def get_media_urls(page: Page):
        print(f'https://123av.com/zh/search?keyword={name}')
        await page.goto(f"https://123av.com/zh/search?keyword={name}")
        print('page')

    async def get_media(page: Page):
        nonlocal mediaUrl
        while mediaUrl == "":
            await asyncio.sleep(0.5)
        if mediaUrl is None:
            raise Exception('not found')
        await page.goto(mediaUrl, wait_until='networkidle')
        mediaUrl = ""

    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=True, proxy={'server': proxy})  # headless=True 表示不显示浏览器窗口
        page = await browser.new_page(ignore_https_errors=True)

        # 设置自定义请求头
        await page.set_extra_http_headers(headers)
        # 绑定事件监听器
        page.on('response', on_response)

        tasks = [get_media_urls(page), get_media(page)]
        await asyncio.gather(*tasks)


async def getImageByMissav(mediaInfo: MediaInfo):
    name = mediaInfo.name
    mediaUrl = ""
    coverUrl = ""

    async def on_response(response: Response):
        nonlocal mediaUrl
        nonlocal mediaInfo
        nonlocal coverUrl

        if 'cover-n' in response.url:
            if name.lower() in response.url:
                mediaInfo.coverImg = await response.body()

        elif response.request.resource_type == 'document':
            if name.lower() not in response.url.lower():
                return
            if 'search' in response.url:
                # print(f"搜索结果：{response.url}")
                # print(await response.text())
                html = etree.HTML(await response.text())
                mediaUrl = html.xpath('/html/body/div[1]/div[3]/div[2]/div/div/div[1]/a[2]/@href')
                if not mediaUrl:
                    mediaUrl = None
                    return
                mediaUrl = mediaUrl[0]
            else:
                html = etree.HTML(await response.text())
                magnets = html.xpath("//td/a[@rel='nofollow']/@href")
                magnets = [re.search(r'magnet:\?xt=urn:btih:\w+', magnet)[0] for magnet in magnets]
                titles = html.xpath("//td/a[@rel='nofollow']/text()")
                length = min(len(magnets), 3)

                for title, magnet in zip(titles[:length], magnets[:length]):
                    print(f"磁力链接：{magnet}")
                    mediaInfo.magnets.add(Magnet(title=title.replace('\n', '').strip(), magnet=magnet))

                coverUrl = html.xpath("/html/head/meta[13]/@content")[0]

    async def get_media_urls(page: Page):
        # print(f"https://missav.com/ja/search/{name}")
        await page.goto("https://missav.live/ja/search/" + name)

    async def get_magnet(page: Page):
        nonlocal mediaUrl
        while mediaUrl == "":
            await asyncio.sleep(0.5)
        if mediaUrl is None:
            raise Exception('not found')
        await page.goto(mediaUrl)

    async def get_cover_img(page: Page):
        nonlocal coverUrl
        while coverUrl == "":
            await asyncio.sleep(0.5)

        await page.goto(coverUrl, wait_until='networkidle')
        await asyncio.sleep(3)

    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=True, proxy={'server': proxy})  # headless=True 表示不显示浏览器窗口
        page = await browser.new_page(ignore_https_errors=True)

        # 设置自定义请求头
        await page.set_extra_http_headers(headers)
        # 绑定事件监听器
        page.on('response', on_response)

        tasks = [get_media_urls(page), get_magnet(page), get_cover_img(page)]

        await asyncio.gather(*tasks)


async def getMagnetByClm9(mediaInfo: MediaInfo):
    name = mediaInfo.name
    # https://clm9.in/search?word=RkMyLVBQVi0xMzk5MDQ5
    key = base64.b64encode(name.encode('utf-8')).decode('utf-8')  # 编码
    search = f"https://clm9.in/search?word={key}"
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(search, headers=headers)
            # print(response.text)
            # response = re.search(r'(?<=window.atob\(")[\w=]+(?="\)\))', response.text).group()
    except Exception as e:
        return []

    # baseResponse = base64.b64decode(response).decode('utf-8')
    html = etree.HTML(response.text)

    movies = []
    results = html.xpath('//*[@id="Search_list_wrapper"]/li')
    for result in results:
        url = 'https://clm9.in' + result.xpath('./div[1]/div/a/@href')[0]
        title = result.xpath('./div[1]/div/a')[0].xpath('string()')
        desc = '下载次数' + result.xpath('./div[2]')[0].xpath('string()')
        movies.append((url, title, desc))

    # logger.info(f"搜索结果：{movies}")

    name = name.lower().replace('-', '').replace('_', '')
    newMovies = []
    for movie in movies:
        title = movie[1].lower().replace('-', '').replace('_', '')
        if name in title and 'C' in title:
            newMovies.insert(0, movie)
        elif name in title:
            newMovies.append(movie)
    movies = newMovies
    if len(movies) >= 3:
        movies = movies[:3]

    async def _getResult(movie):
        magnetHeaders = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9,ja;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'max-age=0',
            # 'cookie'                   : 'PHPSESSID=7695uj4bor9ombg9ljflbr3ivb; mem_tip=1',
            'priority': 'u=0, i',
            'referer': 'https://clm9.in/search?word=RkMyLVBQVi0xMzk5MDQ5',
            'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Linux"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
        }
        try:
            async with httpx.AsyncClient() as client:
                magnetResponse = await client.get(movie[0], headers=magnetHeaders)
                # print(magnetResponse.text)
                # magnetResponse = re.search(r'(?<=window.atob\(")[\w=+]+(?="\)\))', magnetResponse.text).group()
                magnet = \
                    re.findall(r"magnet:\?xt=urn:btih:\w+", magnetResponse.text)[
                        0]

        except Exception as e:
            print(e, movie)
            return []
        else:
            print(f"磁力链接：{movie[0]}")
            print(f"磁力链接：{magnet}")
            return [magnet, movie[1], movie[2]]

    tasks = [_getResult(movie) for movie in movies]

    results = await asyncio.gather(*tasks)
    for result in results:
        if result is not None:
            mediaInfo.magnets.add(Magnet(title=result[1], magnet=result[0], fileinfo=result[2]))


async def getMagnetByCililianjie(mediaInfo: MediaInfo):
    name = mediaInfo.name.lower()
    searchUrl = f"https://www.cililianjie.org/search?q={name}"
    print(searchUrl)
    async with httpx.AsyncClient() as client:
        response = await client.get(searchUrl, headers=headers)
        nodes = etree.HTML(response.text).xpath('/html/body/div[2]/ul/li')

        results = []
        for node in nodes:
            title = node.xpath('./div[1]/div[1]')
            if not title:
                return
            title = title[0].xpath('string()')
            file = f"包含文件{node.xpath('./div[1]/div[2]')[0].xpath('string()')}，大小{node.xpath('./div[2]/text()')[0]}"
            url = "https://www.cililianjie.org" + node.xpath('./a/@href')[0]
            results.append([title, file, url])

        async def _getResult(result):
            _response = await client.get(result[2], headers=headers)
            _magnet = re.findall(r"magnet:\?xt=urn:btih:\w*", _response.text)[0]
            print(f"磁力链接：{result[2]}")
            print(f"磁力链接：{_magnet}")
            return [_magnet, result[0], result[1]]

        results = [result for result in results
                   if name.strip().lower().replace('-', '').replace('_', '')
                   in result[0].strip().lower().replace('-', '').replace('_', '')]

        temp = []
        for result in results:
            if "C" in result[0]:
                temp.insert(0, result)
            else:
                temp.append(result)
        results = temp
        if len(results) >= 3:
            results = results[:3]

        tasks = [_getResult(result) for result in results]
        magnets = await asyncio.gather(*tasks)
        for magnet in magnets:
            mediaInfo.magnets.add(Magnet(title=magnet[1], magnet=magnet[0], fileinfo=magnet[2]))


async def startSpider(name: str) -> MediaInfo:
    mediaInfo = MediaInfo(name=name)

    tasks = [
        # getMagnetByClm9(mediaInfo),  # 磁力
        # getMagnetByCililianjie(mediaInfo),  # 磁力
        getImageBy123av(mediaInfo),  # 封面、预览
        # getImageByMissav(mediaInfo),  # 封面、磁力
    ]

    await asyncio.gather(*tasks, return_exceptions=True)

    return mediaInfo


if __name__ == '__main__':
    # asyncio.run(startSpider("SSNI-777"))
    # asyncio.run(startSpider("RCT-860"))
    mediaInfo = asyncio.run(startSpider("DFDM-060"))
    print(mediaInfo.magnets)
    print(type(mediaInfo.coverImg))
    print(type(mediaInfo.previewImg))
    # proxies = {
    #     "http": "http://127.0.0.1:7890",
    #     "https": "http://127.0.0.1:7890",
    # }
    # response = requests.get('https://missav.live/ja/search/ipx-246', headers=headers, proxies=proxies)
    # print(response.status_code)
