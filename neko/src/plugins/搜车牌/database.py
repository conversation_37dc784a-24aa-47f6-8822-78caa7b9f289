import io
import os
import pickle
import sqlite3
import sys
from contextlib import asynccontextmanager
from typing import List, Optional, Union

try:
    from .pojo import MediaInfo, Magnet
except ImportError:
    from pojo import MediaInfo, <PERSON>gnet
from minio import Minio
from nonebot import get_plugin_config
from pydantic import BaseModel, Field


class Config(BaseModel):
    minio_end_point: Optional[str] = Field(default=None)
    minio_access_key: Optional[str] = Field(default=None)
    minio_secret_key: Optional[str] = Field(default=None)


plugin_config: Config = get_plugin_config(Config)

minio_client = Minio(
    endpoint=plugin_config.minio_end_point,
    access_key=plugin_config.minio_access_key,
    secret_key=plugin_config.minio_secret_key,
    secure=False,
)


class SQLiteManager:
    def __init__(self, db_path: str):
        """
        初始化数据库连接
        :param db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.connection = sqlite3.connect(self.db_path)
        self.cursor = self.connection.cursor()
        self._create_table()
        self.data_path = "car-img/"

    def _create_table(self):
        """
        创建表
        """
        create_table_query = """
                             CREATE TABLE IF NOT EXISTS magnet
                             (
                                 id         INTEGER PRIMARY KEY AUTOINCREMENT,
                                 name       TEXT NOT NULL UNIQUE,
                                 magnets    BLOB NOT NULL,
                                 coverImg   TEXT,
                                 previewImg TEXT
                             ); \
                             """
        self.cursor.execute(create_table_query)
        self.connection.commit()

    def _byte2file(self, byte_data: Union[bytes, None], file_name: str):
        """
        将二进制数据写入文件
        :param byte_data: 二进制数据
        :param file_name: 文件名
        :return:
        """
        if byte_data is None:
            return

        minio_client.put_object('resource', self.data_path + file_name, data=io.BytesIO(byte_data),
                                length=len(byte_data))

    def _file2byte(self, file_name: str) -> Union[bytes, None]:
        """
        读取文件内容并返回二进制数据
        :param file_name: 文件名
        :return: 二进制数据
        """
        # print(self.data_path + file_name)
        try:
            minio_client.stat_object('resource', self.data_path + file_name)
        except Exception as e:
            # print(e)
            return None
        obj = minio_client.get_object('resource', self.data_path + file_name)
        # print(len(obj.read()))
        return obj.read()

    def insert_item(self, mediaInfo: MediaInfo):
        """
        插入一条记录
        :param mediaInfo: MediaInfo 对象
        """
        name = mediaInfo.name.strip().lower().replace('-', '').replace('_', '')
        coverImg_name = f"{name}_cover.png"
        previewImg_name = f"{name}_preview.png"

        self._byte2file(mediaInfo.coverImg, coverImg_name)
        self._byte2file(mediaInfo.previewImg, previewImg_name)

        insert_query = """
                       INSERT INTO magnet (name, magnets, coverImg, previewImg)
                       VALUES (?, ?, ?, ?); \
                       """
        if self.get_item_by_name(name):
            print(f"跳过插入，名称{name}已存在")
            return False
        else:
            self.cursor.execute(insert_query,
                                (name, pickle.dumps(mediaInfo.getListMagnets()), coverImg_name, previewImg_name))
            self.connection.commit()
            return True

    def get_item_by_id(self, item_id: int) -> Optional[MediaInfo]:
        """
        根据 ID 查询一条记录
        :param item_id: 记录的 ID
        :return: 返回包含记录的字典，如果未找到则返回 None
        """
        select_query = """
                       SELECT name, magnets, coverImg, previewImg
                       FROM magnet
                       WHERE id = ?; \
                       """
        self.cursor.execute(select_query, (item_id,))
        row = self.cursor.fetchone()
        if row:
            # 将 magnets 从 JSON 字符串转换回列表
            return MediaInfo(
                name=row[0],
                magnets=pickle.loads(row[1]),
                coverImg=self._file2byte(row[2]),
                previewImg=self._file2byte(row[3]),
            )
        return None

    def get_all_items(self) -> List[dict]:
        """
        查询所有记录
        :return: 返回包含所有记录的列表
        """
        select_query = """
                       SELECT id, name
                       FROM magnet; \
                       """
        self.cursor.execute(select_query)
        rows = self.cursor.fetchall()
        items = []
        for row in rows:
            items.append({
                "id": row[0],
                "name": row[1],
            })
        return items

    def get_item_by_name(self, name: str) -> Optional[MediaInfo]:
        """
        根据名称查询一条记录
        :param name: 记录的名称
        :return: 返回包含记录的字典，如果未找到则返回 None
        """
        name = name.strip().lower().replace('-', '').replace('_', '')
        select_query = """
                       SELECT id, name, magnets, coverImg, previewImg
                       FROM magnet
                       WHERE name = ?; \
                       """
        self.cursor.execute(select_query, (name,))
        row = self.cursor.fetchone()
        if row:
            # print(type(row[2]))
            # print(type(pickle.loads(row[2])))
            # 将 magnets 从 JSON 字符串转换回列表
            return MediaInfo(
                name=row[1],
                magnets=pickle.loads(row[2]),
                coverImg=self._file2byte(row[3]),
                previewImg=self._file2byte(row[4]),
            )
        return None

    def update_item_by_id(self, item_id: int, mediaInfo: MediaInfo):
        """
        根据 ID 更新一条记录
        :param item_id: 记录的 ID
        :param mediaInfo: MediaInfo 对象
        """
        name = mediaInfo.name.strip().lower().replace('-', '').replace('_', '')
        coverImg_name = f"{name}_cover.png"
        previewImg_name = f"{name}_preview.png"

        self._byte2file(mediaInfo.coverImg, coverImg_name)
        self._byte2file(mediaInfo.previewImg, previewImg_name)

        update_query = """
                       UPDATE magnet
                       SET name       =?,
                           magnets    = ?,
                           coverImg   = ?,
                           previewImg = ?
                       WHERE id = ?; \
                       """
        self.cursor.execute(update_query,
                            (name, pickle.dumps(mediaInfo.getListMagnets()), coverImg_name, previewImg_name, item_id))
        self.connection.commit()

    def close(self):
        """
        关闭数据库连接
        """
        self.cursor.close()
        self.connection.close()


@asynccontextmanager
async def Manager():
    """
    获取数据库对象
    :return: SQLiteManager 对象
    """
    manager = SQLiteManager(db_path=str(os.path.realpath(__file__).replace(r'database.py', '') + 'data.db'))
    try:
        yield manager
    finally:
        manager.close()


# db = SQLiteManager(db_path=str(os.path.realpath(__file__).replace(r'database.py', '') + 'data.db'))

if __name__ == '__main__':
    async def test():
        async with Manager() as db:
            # 插入一条记录
            items = db.get_all_items()
            for item in items:
                m = db.get_item_by_id(item['id'])
                db.update_item_by_id(item['id'], m)


    import asyncio

    asyncio.run(test())
