from dataclasses import dataclass, field


@dataclass
class Magnet:
	title: str = ''
	magnet: str = ''
	fileinfo: str = ''
	
	def __hash__(self):
		return hash(self.magnet)
	
	def __eq__(self, other):
		if isinstance(other, Magnet):
			return self.magnet == other.magnet
		return False


@dataclass
class MediaInfo:
	name: str = ''
	magnets: set[Magnet] = field(default_factory=set)
	coverImg: bytes = None
	previewImg: bytes = None
	
	def __post_init__(self):
		if self.magnets and type(list(self.magnets)[0]) == list:
			self.magnets = set(
					Magnet(title=title, magnet=magnet, fileinfo=fileinfo) for title, magnet, fileinfo in self.magnets)
	
	def getListMagnets(self):
		return [[magnet.title, magnet.magnet, magnet.fileinfo] for magnet in self.magnets]