import asyncio
import os
import time

import httpx
from lxml import etree

try:
	from .database import Manager
	from .pojo import MediaInfo
	from .spider import transport, headers, startSpider
except ImportError:
	from database import Manager
	from pojo import MediaInfo
	from spider import transport, headers, startSpider

success = 1
async def update(index):
	global success
	# 	https://123av.com/ja/today-hot
	# https://123av.com/ja/today-hot?page=2
	# https://123av.com/ja/weekly-hot
	async with httpx.AsyncClient(transport=transport, timeout=30) as client:
		response = await client.get(f"https://123av.com/ja/dm1/weekly-hot?page={index}", headers=headers)
		responseText = response.text
		html = etree.HTML(responseText)
		names = html.xpath('//*[@class="col-6 col-sm-4 col-lg-3"]/div/div[1]/a/@title')
		for name in names:
			async with <PERSON>() as db:
				if db.get_item_by_name(name):
					print(f"数据库已存在：{name}")
					continue
			print(f"开始更新：{name}")
			# try:
			mediaInfo: MediaInfo = await startSpider(name)
			if not mediaInfo.magnets:
				print(f"无磁链：{name}")
				return
			async with Manager() as db:
				db.insert_item(mediaInfo)
			# except Exception as e:
			# 	print(f" {index}页,更新失败：{name}，{e}")
			# else:
			print(f"开始更新, {index}页, 第{success}次更新成功：{name}")
			success += 1
			await asyncio.sleep(60 * 1)
	index += 1


if __name__ == '__main__':
	index = 55
	while True:
		try:
			asyncio.run(update(index))
		except Exception as e:
			print(e)
			# time.sleep(60 * 10)
		else:
			index += 1
		time.sleep(60 * 10)
# print(db.get_all_items())
