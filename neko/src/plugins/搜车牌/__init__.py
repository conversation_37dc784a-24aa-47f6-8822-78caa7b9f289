import asyncio
from datetime import datetime
import io
import os
import random
import re
import time
import valkey
from PIL import Image
from nonebot import on_command, logger
from nonebot.adapters.onebot.v11 import (
    Bot,
    GroupMessageEvent
)
from nonebot.plugin import PluginMetadata
from nonebot.plugin.on import on_fullmatch

from .database import Manager
from .pojo import Magnet, MediaInfo
from .spider import startSpider

try:
    from nonebot.adapters.onebot.v11 import Message, MessageSegment  # type: ignore
except ImportError:
    from nonebot.adapters.cqhttp import Message, MessageSegment  # type: ignore
__plugin_meta__ = PluginMetadata(
    name="搜车牌",
    description="bro还在为找资源发愁吗",
    usage="搜索指定车牌号的资源：搜车牌 [车牌号]\n随机获得一个资源：随机车牌",
    type="application",
)
file_path = os.path.realpath(__file__).replace(r'__init__.py', '')
valkeyClient = valkey.Valkey(
    host='***********',
    port=6389,
    db=0,
    decode_responses=True,  # 自动将字节解码为字符串
    protocol=2  # 强制使用 RESP2 协议以确保兼容性
    # password='your_secure_password'  # 如果设置了密码，取消注释并填入
    # username='default'  # 如果使用 ACL，指定用户名
)


# 自定义请求头
def concatenate_images_vertically(image1_bytes: bytes, image2_bytes: bytes) -> bytes:
    """
    将两张图片垂直拼接在一起，并返回拼接后的图片字节流
    :param image1_bytes: 第一张图片的字节流
    :param image2_bytes: 第二张图片的字节流
    :return: 拼接后的图片字节流
    """
    # 将字节流转换为图片对象
    image1 = Image.open(io.BytesIO(image1_bytes))
    image2 = Image.open(io.BytesIO(image2_bytes))

    # 确保两张图片的宽度相同（如果不相同，调整第二张图片的宽度）
    if image1.width != image2.width:
        # 计算缩放比例
        scale = image1.width / image2.width
        new_height = int(image2.height * scale)
        # 调整第二张图片的大小
        image2 = image2.resize((image1.width, new_height), Image.Resampling.LANCZOS)

    # 创建一个新的图片，宽度为两张图片的最大宽度，高度为两张图片高度之和
    new_image = Image.new("RGB", (image1.width, image1.height + image2.height))

    # 将两张图片粘贴到新图片中
    new_image.paste(image1, (0, 0))
    new_image.paste(image2, (0, image1.height))

    # 将新图片保存为字节流
    byte_arr = io.BytesIO()
    new_image.save(byte_arr, format="PNG")  # 可以修改为其他格式，如 JPEG
    return byte_arr.getvalue()


def create_gif_from_images(image1_bytes: bytes, image2_bytes: bytes, duration: int = 1000) -> bytes:
    """
    将两张图片合并为一个 GIF，并返回 GIF 的字节流
    :param image1_bytes: 第一张图片的字节流
    :param image2_bytes: 第二张图片的字节流
    :param duration: 每帧的显示时间（毫秒），默认为 500 毫秒
    :return: GIF 的字节流
    """
    # 将字节流转换为图片对象
    image1 = Image.open(io.BytesIO(image1_bytes))
    image2 = Image.open(io.BytesIO(image2_bytes))

    # 确保两张图片的大小相同（如果不相同，调整第二张图片的大小）
    if image1.size != image2.size:
        image2 = image2.resize(image1.size, Image.Resampling.LANCZOS)

    # 创建一个字节流对象
    byte_arr = io.BytesIO()

    # 将两张图片保存为 GIF
    image1.save(
        byte_arr,
        format="GIF",
        save_all=True,  # 保存所有帧
        append_images=[image2],  # 添加第二张图片作为第二帧
        duration=duration,  # 每帧的显示时间
        loop=0,  # 无限循环
    )

    # 返回 GIF 的字节流
    return byte_arr.getvalue()


def getNode(message: Message):
    return MessageSegment.node_custom(
        user_id=2244759730,
        nickname="找资源糕手",
        content=message
    )


async def getMessage(mediaInfo: MediaInfo):
    msgList = []

    if not mediaInfo.magnets:
        return [getNode("未找到影片，名字错误或者网站抽风，过几分钟再试试")]

    if mediaInfo.previewImg is not None:
        new_img = concatenate_images_vertically(mediaInfo.coverImg, mediaInfo.previewImg)
        msgList.append(getNode(MessageSegment.image(new_img)))
    else:
        msgList.append(getNode(MessageSegment.image(mediaInfo.coverImg)))
        msgList.append(getNode("没找到预览图///"))

    s = f"统一资源名称：{mediaInfo.name}"
    for magnet in mediaInfo.magnets:
        s += f"\n\n标题：{magnet.title}链接：{magnet.magnet}"
    msgList.append(getNode(s))

    logger.info(f"存储数据：{mediaInfo.name}")

    async with Manager() as db:
        db.insert_item(mediaInfo)

    return msgList


match_data = {}

random_key = on_fullmatch("随机车牌", priority=20, block=True)


@random_key.handle()
async def random_resource(bot: Bot, event: GroupMessageEvent):
    uid = event.get_user_id()
    key = f'qqbot:scar:cnt{datetime.now().strftime("%m%d")}'
    valkeyClient.hsetnx(key, uid, 0)
    valkeyClient.expire(key, 24*3600, nx=True)
    result = valkeyClient.hincrby(key, uid)

    if result > 3:
        await random_key.finish("你今天已经随机了3次了，请明天再来")

    async with Manager() as db:
        items = db.get_all_items()
        choice = random.choice(items)
        logger.info(f"随机资源：id={choice['id']}, name={choice['name']}")
        mediaInfo = db.get_item_by_id(choice['id'])
    # result = db.get_item_by_id(72)

    msgList = await getMessage(mediaInfo)
    msgList.append(getNode(f"数据库目前收纳{len(items)}个资源"))
    # logger.info(f"发送数据：{mediaInfo.name}")
    # logger.info(f"发送数量：{len(mediaInfo.magnets)}")
    # logger.info(f"{type(mediaInfo.coverImg)}")
    # logger.info(f"{type(mediaInfo.previewImg)}")

    try:
        await bot.send_group_forward_msg(group_id=str(event.group_id), messages=msgList)
    except Exception as e:
        logger.error(f"发送失败：{mediaInfo.name}，{e}")
        msgList[0] = getNode("图片被风控了，取消发送")
        try:
            await bot.send_group_forward_msg(group_id=str(event.group_id), message=msgList)
        except Exception as e:
            await bot.send_group_forward_msg(group_id=str(event.group_id), message="发送失败")


key = on_command("搜车牌", priority=10, block=True)
@key.handle()
async def register(bot: Bot, event: GroupMessageEvent):
    global match_data
    event_user = event.get_user_id()
    groupId = str(event.group_id)
    msg = str(event.get_message())
    # 冷却时间
    if event_user in match_data.keys():
        if int(time.time()) - match_data[event_user] < 30:
            await key.send("冷却中...")
            return
        else:
            match_data[event_user] = int(time.time())
    # 准备数据
    name = re.search(r'(?<=搜车牌)[ a-zA-Z0-9\-_]+', msg)
    print(msg)
    if name is None and msg == '搜车牌':
        await key.finish("请输入车牌号")
    name = name.group()
    if not name:
        await key.finish('瞎鸡巴搜尼马呢')

    logger.info(f"车牌号：{name}")
    # 判断是否存在
    await key.send(f"正在搜索...{name}...耐心等待...")

    async with Manager() as db:
        mediaInfo = db.get_item_by_name(name)

    if mediaInfo:
        msgList = await getMessage(mediaInfo)
        msgList.append(getNode("命中数据库"))

        logger.info(f"数据库已存在：{name}")
        try:
            await bot.send_group_forward_msg(group_id=groupId, messages=msgList)
        except Exception as e:
            logger.error(f"发送失败：{name}，{e}")
            msgList[0] = getNode("图片被风控了，取消发送")
            await bot.send_group_forward_msg(group_id=groupId, message=msgList)
        return

    # 不存在就开始搜索
    try:
        mediaInfo = await startSpider(name)
    except Exception as e:
        logger.error(f"搜索失败：{name}，{e}")
        await key.finish("代理失败或者网站抽风，过几分钟再试试")

    msgList = await getMessage(mediaInfo)

    try:
        await bot.send_group_forward_msg(group_id=groupId, messages=msgList)
    except Exception as e:
        logger.error(f"发送失败：{name}，{e}")
        msgList[0] = getNode("图片被风控了，取消发送")
        try:
            await bot.send_group_forward_msg(group_id=str(event.group_id), message=msgList)
        except Exception as e:
            bot.send_group_forward_msg(group_id=str(event.group_id), message="发送失败")
# await update()
