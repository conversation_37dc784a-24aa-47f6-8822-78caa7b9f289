# neko/plugins/舔狗日记/dog.txt
import os
import random
import nonebot
from nonebot.adapters.onebot.v11 import Bo<PERSON>, Event, MessageSegment
from nonebot.adapters.onebot.v11.message import Message
from nonebot.plugin import PluginMetadata
import boto3
from nonebot.plugin.on import on_fullmatch

__plugin_meta__ = PluginMetadata(
		name="一眼丁真",
		description="随机发送一张丁真图",
		usage="随机抽取：一眼丁真",
		type='application',
		extra={
				"author"       : "wzz",
				"menu_data"    : [
						{
								"func"             : "抽取图片",
								"trigger_method"   : "命令：一眼丁真",
								"trigger_condition": "完全匹配[Any]",
								"brief_des"        : "随机抽取",
								"detail_des"       : "无",
						},
				],
				"menu_template": "default",
		},
)

from minio import Minio
from typing import Optional
from nonebot import get_plugin_config
from pydantic import BaseModel, Field


class Config(BaseModel):
	minio_end_point: Optional[str] = Field(default=None)
	minio_access_key: Optional[str] = Field(default=None)
	minio_secret_key: Optional[str] = Field(default=None)


plugin_config: Config = get_plugin_config(Config)

minio_client = Minio(
		endpoint=plugin_config.minio_end_point,
		access_key=plugin_config.minio_access_key,
		secret_key=plugin_config.minio_secret_key,
		secure=False,
)

key = on_fullmatch('一眼丁真', priority=50)


@key.handle()
async def key_handle(bot: Bot, event: Event):
	img = random.choice(list(minio_client.list_objects('resource', prefix='dingzhen-img/', recursive=True)))
	
	data = minio_client.get_object('resource', img.object_name)
	content = data.read()
	data.close()

	await key.send(message=MessageSegment.image(content))
	# await key.send(message=MessageSegment.video('/media/movies/nsfw/anisora/video_b1e0b428-6296-4a02-a1ca-79fe5a8fdd25_1752028418.mp4'))
