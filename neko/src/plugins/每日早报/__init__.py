import asyncio
import random

import nonebot
import requests
from nonebot.plugin import PluginMetadata

from nonebot_plugin_apscheduler import scheduler

__plugin_meta__ = PluginMetadata(
		name="每日早报",
		description="睁眼看世界",
		usage="每日定时发送，无需主动触发",
		type="application",
)


class Time():
	def __init__(self, hour, minute):
		self.hour: int = hour
		self.minute: int = minute

	def __str__(self):
		return f"{self.hour}:{self.minute}"


plugin_config = [
		[597015006, Time(9, 11)],  # python群
		[839497425, Time(9, 16)],  # 正人君子
		[616978142, Time(9, 21)],  # 魔怔人
		[868475842, Time(9, 26)],  # 龙河群
]


def morning_paper() -> str:
	newsResponse = requests.get("https://v2.alapi.cn/api/zaobao?token=qFOUiHorbEC5Z5Mt").json()["data"]
	data = ""
	date = newsResponse["date"]
	newsList = newsResponse["news"]

	weiyu = newsResponse["weiyu"]
	data += ("早上好，以下是{}日早报：\n".format(date))
	for x in newsList:
		data += "{}\n".format(x)
	data += weiyu

	return data


# print(messageList)

async def job(group_id):
	await nonebot.get_bot().send_group_msg(group_id=group_id, message=morning_paper())


for index, msgConfig in enumerate(plugin_config):
	time: Time = msgConfig[1]
	nonebot.logger.info("id:{},time:{}".format(index, time))
	scheduler.add_job(job, "cron", hour=time.hour, minute=time.minute, id=str(index), args=[msgConfig[0]])
