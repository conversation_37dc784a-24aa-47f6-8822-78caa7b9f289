<!DOCTYPE html>

<head>
  <meta charset="utf8">
  <link rel="stylesheet" href="css/weather.css">
  <link rel="stylesheet" href="css/qweather-icons.css">
  <script>
window.onload = function () {
    color();
};
function color() {
    var date = new Date();
    var current_hour = date.getHours();

    if (!(current_hour >= 6 && current_hour < 18)) {
        document.body.style.backgroundImage = "linear-gradient(rgb(25 23 58), rgb(25 29 45))";
        document.getElementById("today-weather").style.backgroundImage = "linear-gradient(225deg, #0d3447, #0e0e2c)";
        document.getElementById("air").style.backgroundImage = "linear-gradient(225deg, #191a5b, #090b1e)";
        document.getElementById("hours-weather").style.backgroundImage = "linear-gradient(225deg, #0d294b, #1c1c27)";
        document.getElementById("days-weather").style.backgroundImage = "linear-gradient(225deg, #0f2a4b, #0f0f14)";
        air = document.getElementsByName("warning-box");
        for (var i = 0; i <air.length; i++) {
          air[i].style.backgroundImage = "linear-gradient(225deg, #55112a, #160511)";
        }
        document.body.style.color = "#cecece";
    }
}
</script>
</head>

<body>
  <div class="main">
    <div class="today-weather" id="today-weather">
      <div class="today-top">
        <div class="text-center">
          <h1 class="city">{{ city }}</h1>
          <p class="realtime-tem">{{ now['temp'] }}°</p>
        </div>

        <div class="text-center">
          <p class="obs-time">{{ now['obsTime'] }}</p>
          <em class="qi-{{ now['icon'] }}"></em>
          <p class="realtime-text">{{ now['text'] }}</p>
        </div>
      </div>



      <div class="today-button">
        <div>
          <p>{{ now['windScale'] }}级</p>
          <p class="des">{{ now['windDir'] }}</p>
        </div>
        <div>
          <p>{{ now['humidity'] }}%</p>
          <p class="des">相对湿度</p>
        </div>
        <div>
          <p>{{ now['precip'] }}mm</p>
          <p class="des">降水量</p>
        </div>
        <div>
          <p>{{ now['vis'] }}km</p>
          <p class="des">能见度</p>
        </div>
      </div>
    </div>

    {% for w in warning["warning"] %}
    <div class="warning-box" id="warning-box" name="warning-box">
      <div class="warning-side">
        <div class="warning-icon"><em class="qi-{{ w['type'] }}"></em></div>
        <div class="warning-header">
          <p class="warning-title">{{ w['title'] }}</p>
          <p class="warning-time">{{ w['pubTime'] }}</p>
        </div>
      </div>
      <div class="warning-info">
        <p>{{ w['text'] }}</p>
      </div>
    </div>
    {% endfor %}

    {% if air %}
    <div class="air" id="air">
      <div class="air-box-top">
          <span class="air-tag" style="background-color:{{ air['tag_color'] }};">{{ air["aqi"] }} {{ air["category"] }}</span>
      </div>
      <div class="air-box">
          <div class="air-data">
              <p>{{ air["pm2p5"] }}</p>
              <meter value="{{ air['pm2p5'] }}" min="0" max="200"></meter>
              <p class="des">PM2.5</p>
          </div>
          <div class="air-data">
              <p>{{ air["pm10"] }}</p>
              <meter value="{{ air['pm10'] }}" min="0" max="200"></meter>
              <p class="des">PM10</p>
          </div>
          <div class="air-data">
              <p>{{ air["o3"] }}</p>
              <meter value="{{ air['o3'] }}" min="0" max="200"></meter>
              <p class="des">O<sub>3</sub></p>
          </div>
          <div class="air-data">
              <p>{{ air["co"] }}</p>
              <meter value="{{ air['co'] }}" min="0" max="200"></meter>
              <p class="des">CO</p>
          </div>
          <div class="air-data">
              <p>{{ air["no2"] }}</p>
              <meter value="{{ air['no2'] }}" min="0" max="200"></meter>
              <p class="des">NO<sub>2</sub></p>
          </div>
          <div class="air-data">
              <p>{{ air["so2"] }}</p>
              <meter value="{{ air['so2'] }}" min="0" max="200"></meter>
              <p class="des">SO<sub>2</sub></p>
          </div>
      </div>
  </div>
  {% endif %}

    <div class="hours-weather" id="hours-weather">
      <div class="hour-box">
        {% for hour in hours %}
        <div class="hour-data">
          <div class="hour-data">      
            <p>{{ hour["temp"] }}°</p>
            <div class="tem-vert-line-box"><div class="tem-vert-line" style="height: {{ hour['temp_percent'] }};"></div></div>
            <p class="qi-{{ hour['icon'] }} icon-size"></p>
            <p class="hour-des">{{ hour["hour"] }}</p>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>

    <div class="days-weather" id="days-weather">
      {% for day in days %}
      <div class="days">
        <!-- 1 -->
        <div class="week-date">
          <p class="weeks">{{ day["week"] }}</p>
          <p class="date">{{ day["date"] }}</p>
        </div>
        <!-- 2 -->
        <div class="tem-text">
          <p class="days-temp">{{ day["tempMax"] }}°</p>
          <p class="days-text">{{ day["textDay"] }}</p>
        </div>
        <!-- 3 -->
        <div class="tem-line-box">

          <div class="days-icon">
            <div>
              <em class="qi-{{ day['iconDay'] }} icon-size icon-day"></em>

            </div>
            <div class="icon-night">
              <em class="qi-{{ day['iconNight'] }} icon-size icon-night"></em>
            </div>
          </div>

          <div class="tem-line-outbox">
            <div class="tem-line"></div>
          </div>

        </div>
        <!-- 4 -->
        <div class="tem-text">
          <p class="days-temp">{{ day["tempMin"] }}°</p>
          <p class="days-text">{{ day["textNight"]}}</p>

        </div>
      </div>
      {% endfor %}
    </div>
  </div>
  </div>
</body>
