body {
  background-image: linear-gradient(to bottom, #bdc3c7, #2c3e50);
  color: white;
  padding: 10px;
}

.today-weather {
  padding: 20px;
  border-radius: 20px;
  background-image: linear-gradient(225deg, #2193b0, #6dd5ed);
}

h1 {
  padding-top: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  font-size: 40px;
}

p {
  padding-top: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
  font-size: 30px;
}

.today-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 40px;
}

.text-center {
  text-align: center;
}

.realtime-tem {
  font-size: 100px;
}

.obs-time {
  font-size: 20px;
  margin-bottom: 5px !important;
  color: rgba(232, 230, 227, 0.55);
}

em {
  font-size: 100px;
}

p.realtime-text {
  font-size: 35px;
}

.today-button {
  margin-top: 5px;
  background-color: #0000001e;
  border-radius: 20px;
  padding: 20px 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.des {
  font-size: 30px;
  color: rgba(232, 230, 227, 0.55);
}

.icon-size {
  font-size: 60px;
}

.date {
  color: rgba(232, 230, 227, 0.55);
  font-size: 20px;
  text-size-adjust: 100%;
}

.weeks {
  font-size: 30px;
}

.days-weather {
  padding: 20px 50px;
  border-radius: 20px;
  margin-top: 20px;
  background-image: linear-gradient(225deg, #0f2027, #2c5364);
}

.tem-line {
  height: 10px;
  border-radius: 10px;
  background-image: linear-gradient(to right, #f18360, #427bff);
}

.days-icon {
  column-count: 2;
}

.icon-night {
  text-align: right;
}

.days {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
}

.tem-line-box {
  width: 250px;
}

.days-temp {
  font-size: 40px;
}

.days-text {
  font-size: 30px;
}

.warning-box {
  padding: 20px;
  border-radius: 20px;
  background-image: linear-gradient(225deg, #b78c579d, #f2d5b2b6);
  margin-top: 20px;
}
.warning-side {
  display: flex;
  align-items: center;
}

.warning-header {
  padding-left: 30px;
}

.warning-time {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.55);
}
.warning-info {
  padding: 20px;
}
.tem-text {
  width: 120px;
  text-align: right;
}

.week-date {
  width: 90px;
}

.air {
  padding: 20px;
  border-radius: 20px;
  background-image: linear-gradient(225deg, #7474bf, #348ac7);
  margin-top: 20px;
}

.air-box-top {
  text-align: center;
}

.air-tag {
  display: inline-block;
  width: intrinsic; /* Safari/WebKit 使用了非标准的名称 */
  width: -moz-max-content; /* Firefox/Gecko */
  width: -webkit-max-content; /* Chrome */
  padding: 4px 32px;
  font-size: 30px;
  line-height: 32px;
  text-align: center;
  white-space: nowrap;
  border-radius: 32px;
  color: white;
}

.air-box {
  display: flex;
  justify-content: space-between;
}

.air-data {
  text-align: center;
  padding: 20px 20px;
  width: 120px;
}

meter {
  width: 100px;
}

.hours-weather {
  padding: 20px 20px;
  border-radius: 20px;
  margin-top: 20px;
  background-image: linear-gradient(225deg, #102833, #2c6279);
}

.hour-box {
  display: flex;
  justify-content: space-between;
}

.tem-vert-line-box {
  height: 100px;
  position: relative;
}

.tem-vert-line {
  width: 10px;
  border-radius: 10px;
  background:  #427bff;
  margin: 0 auto;
  bottom: 0;
  left: 0;
  right: 0;
  position: absolute;
}

.hour-data {
  text-align: center;
  width: 60px;
}
