import json
import os

from nonebot.adapters.onebot.v11 import (
	Bot,
	NoticeEvent, GroupMessageEvent,
	Message, MessageSegment,
	GROUP_OWNER, GROUP_ADMIN
)
from nonebot.matcher import Matcher
from nonebot.params import Depends
from nonebot.permission import SUPERUSER
from nonebot.plugin import on_notice, on_command, on_regex
from nonebot.plugin import PluginMetadata

__plugin_meta__ = PluginMetadata(
		name="进群提醒",
		description="自动进群提醒",
		usage="超管和群管理层可以设置",
		type="application",
		extra={
				"author"       : "wzz",
				"menu_data"    : [
						{
								"func"             : "进群提醒",
								"trigger_method"   : "进群提醒 [提醒内容]",
								"trigger_condition": "开头匹配[SUPERUSER,GROUP_OWNER,GROUP_ADMIN]",
								"brief_des"        : "设置进群提醒，新群员自动触发",
								"detail_des"       : "无",
						},
						{
								"func"             : "查询进群提醒",
								"trigger_method"   : "查询进群提醒",
								"trigger_condition": "完全匹配[ANY]",
								"brief_des"        : "查看本群进群设置",
								"detail_des"       : "无",
						},
				],
				"menu_template": "default",
		},
)
GroupMemNotice = on_notice(priority=5)
CustomGroupIncreaseNotice = on_command("进群提醒", permission=SUPERUSER | GROUP_OWNER | GROUP_ADMIN, priority=5)
SearchUpGroupIncreaseNotice = on_regex("^/?查询进群提醒$", priority=5)
dataPath = os.path.join(os.path.dirname(__file__), "resource") + "/data.json"
noticePath = os.path.join(os.path.dirname(__file__), "resource") + "/GroupIncreaseNotice.json"


async def group_notice_checker(matcher: Matcher, event: NoticeEvent):
	if event.is_tome():
		await matcher.skip()

	try:
		session_id = event.get_session_id()
		group_id = session_id.split('_')[1]
		return group_id
	except (ValueError, IndexError):
		await matcher.skip()


@GroupMemNotice.handle()
async def give_notice(bot: Bot, event: NoticeEvent, group_id: str = Depends(group_notice_checker)):
	user_id = event.get_user_id()
	user_info = await bot.get_stranger_info(user_id=int(user_id))
	user_profile_pic_url = f"https://q.qlogo.cn/g?b=qq&nk={user_id}&s=640"

	try:
		with open(dataPath, "r", encoding="GBK") as f:
			cfg = json.load(f)
		notice_content = cfg[group_id]
	except (FileNotFoundError, KeyError):
		# notice_content = "新人快跑，这是homo群！"
		return

	description = json.loads(event.get_event_description().replace("'", '"'))

	if description['notice_type'] == 'group_increase':
		await GroupMemNotice.finish(Message(f"[CQ:image,file={user_profile_pic_url}]") +
		                            MessageSegment.at(user_id) + f"({user_id}) " + Message(notice_content))

	elif description['notice_type'] == 'group_decrease':
		if description['sub_type'] == 'leave':
			await GroupMemNotice.finish(Message(f"[CQ:image,file={user_profile_pic_url}]") +
			                            f"滴！ {user_info['nickname']}({user_id}) 悄咪咪的离开了我们..")

		elif description['sub_type'] == 'kick':
			op_user_id = description['operator_id']

			await GroupMemNotice.finish(Message(f"[CQ:image,file={user_profile_pic_url}]") +
			                            "目睹了 " + MessageSegment.at(op_user_id) + f"({op_user_id})"
			                                                                        f" 面无表情地把 {user_info['nickname']}({user_id}) 踹了出去的案发现场，当时害怕极了喵。。")


@CustomGroupIncreaseNotice.handle()
async def register(bot: Bot, event: GroupMessageEvent):
	group_id = event.get_session_id().split('_')[1]
	try:
		content = str(event.get_message()).split(maxsplit=1)[1]
	except IndexError:
		await CustomGroupIncreaseNotice.finish("设置进群提醒失败，可能格式错误！")

	try:
		with open(dataPath, "r", encoding="utf8") as f:
			cfg = json.load(f)
	except FileNotFoundError:
		cfg = {}

	if content == "默认":
		try:
			cfg.pop(group_id)
		except:
			pass
	else:
		cfg[group_id] = content

	if not os.path.exists("database"):
		os.mkdir("database")
	with open(noticePath, "w", encoding="GBK") as f:
		json.dump(cfg, f)

	await CustomGroupIncreaseNotice.finish("成功设置进群提醒！")


@SearchUpGroupIncreaseNotice.handle()
async def search(bot: Bot, event: GroupMessageEvent):
	group_id = event.get_session_id().split('_')[1]
	try:
		with open(noticePath, "r", encoding="GBK") as f:
			cfg = json.load(f)
		content = cfg[group_id]

		await SearchUpGroupIncreaseNotice.finish("当前设置进群提醒：\n" + Message(content))
	except (FileNotFoundError, KeyError):
		await SearchUpGroupIncreaseNotice.finish("当前未单独设置进群提醒！")
