import os
import asyncio
import os
import random
import time
from dataclasses import dataclass, field
from urllib.parse import unquote
import io

import aiohttp
import minio.error
import nonebot
from lxml.html import fromstring
from nonebot import logger
from nonebot.plugin import PluginMetadata
from nonebot.plugin.on import on_fullmatch
from nonebot_plugin_apscheduler import scheduler

try:
    from nonebot.adapters.onebot.v11 import Bot, Event, Message  # type: ignore
    from nonebot.adapters.onebot.v11.event import (  # type: ignore
        GroupMessageEvent,
        MessageEvent,
    )
except ImportError:
    from nonebot.adapters.cqhttp import Bot, Event, Message  # type: ignore
    from nonebot.adapters.cqhttp.event import GroupMessageEvent, MessageEvent  # type: ignore

try:
    from nonebot.adapters.onebot.v11 import Message, MessageSegment  # type: ignore
except ImportError:
    from nonebot.adapters.cqhttp import Message, MessageSegment  # type: ignore
from minio import Minio
from typing import Optional
from nonebot import get_plugin_config
from pydantic import BaseModel, Field


class Config(BaseModel):
    minio_end_point: Optional[str] = Field(default=None)
    minio_access_key: Optional[str] = Field(default=None)
    minio_secret_key: Optional[str] = Field(default=None)


plugin_config: Config = get_plugin_config(Config)

minio_client = Minio(
    endpoint=plugin_config.minio_end_point,
    access_key=plugin_config.minio_access_key,
    secret_key=plugin_config.minio_secret_key,
    secure=False,
)
__plugin_meta__ = PluginMetadata(
    name="新番咨询",
    description="每日动漫更新定时通知",
    usage="发送‘新番咨询’可主动查看",
    type="application",
)

proxy = 'http://***********:7890'
proxies = {
    'http': proxy,
    'https': proxy
}


@dataclass
class Magnet:
    episode: str
    magnet: str


@dataclass
class Anime:
    title: str
    url: str
    cover: str | bytes
    magnets: list[Magnet] = field(default_factory=list)


class Time:
    def __init__(self, hour, minute):
        self.hour: int = hour
        self.minute: int = minute

    def __str__(self):
        return f"{self.hour}:{self.minute}"


class Mikanani:
    def __init__(self):
        self.cover_path = 'mikan-cover/'

        self.home = "https://mikanani.me"
        self.animes = []
        self.yesterday = time.strftime("%Y/%m/%d", time.localtime(time.time() - 24 * 60 * 60))
        self.sem = asyncio.Semaphore(5)

    async def init(self):
        await self.get_latest_home()

        tasks = [self.get_magnets(anime) for anime in self.animes]

        try:
            await asyncio.gather(*tasks)  # 真正并发执行
        except Exception as e:
            logger.error("休眠之后重试")
            await asyncio.sleep(120)
            await self.init()

        logger.info("所有磁力链接获取完毕")

    async def get_magnets(self, anime: Anime):
        logger.info(f"正在获取《{anime.title}》的磁力链接...")

        async with aiohttp.ClientSession() as session:
            # 获取封面图片
            async with self.sem:
                coverName = self.cover_path + anime.cover.split('/')[-1]

                try:
                    obj = minio_client.stat_object('resource', coverName)
                    logger.info(f"《{anime.title}》封面图片已存在")
                except minio.error.S3Error as e:
                    logger.info(f"《{anime.title}》封面图片不存在, 准备下载")
                    async with session.get(anime.cover, proxy=proxy) as response:
                        coverImg = await response.read()
                        minio_client.put_object(
                            'resource',
                            object_name=coverName,
                            data=io.BytesIO(coverImg),
                            length=len(coverImg),
                            content_type='image/jpg'
                        )

                    logger.info(f"《{anime.title}》封面图片下载完毕")

                obj = minio_client.get_object('resource', coverName)
                anime.cover = obj.read()

                obj.close()

            await asyncio.sleep(10 + random.randint(0, 5))

            # 抓取磁力链接
            async with self.sem:
                async with session.get(anime.url, proxy=proxy) as response:
                    html = fromstring(await response.text())

                    magnetInfos = html.xpath('//div[@class="sk-col res-date"]')

                    for magnetInfo in magnetInfos:

                        if not magnetInfo.xpath('./text()')[0].startswith(self.yesterday):
                            continue

                        magnet = Magnet(
                            episode=magnetInfo.xpath('preceding-sibling::div[1]/a[1]/text()')[0],
                            magnet=unquote(magnetInfo.xpath(
                                'preceding-sibling::div[1]/a[2]/@data-clipboard-text')[0])
                        )

                        anime.magnets.append(magnet)

                logger.info(f"《{anime.title}》的磁力链接获取完毕, 共{len(anime.magnets)}个")

    # 获取每日最新页面

    async def get_latest_home(self):
        logger.info("正在获取最新动漫更新信息...")

        async with aiohttp.ClientSession() as session:
            async with session.get(self.home, proxy=proxy) as response:
                html = fromstring(await response.text())

                # 番剧更新信息
                update_info = html.xpath('//div[@class="an-info-group"]')

                # 昨天的日期 格式：2022/01/01
                for info in update_info:
                    if not info.xpath('./div[1]/text()')[0].startswith(self.yesterday):
                        continue

                    self.animes.append(
                        Anime(
                            title=info.xpath('./a/@title')[0],
                            url=self.home + info.xpath('./a/@href')[0].replace('Bangumi/',
                                                                               'ExpandBangumi?bangumiId=') + '&showSubscribed=false',
                            cover=self.home + info.xpath('../../span/@data-src')[0].split('?')[0]
                        )
                    )

        logger.info(f"获取{len(self.animes)}部动漫更新信息")


def getNode(content: Message, nickname: str = "冻鳗糕手"):
    """生成转发消息节点"""
    return MessageSegment.node_custom(
        user_id=2244759730,
        nickname=nickname,
        content=content
    )


async def job(group_id: int):
    mikanani = Mikanani()
    await mikanani.init()

    # 主消息列表（第一条是标题）
    total = '昨日动漫资源更新汇总:'
    msgList = []

    animeNode = []
    # 遍历每一部动漫
    for i, anime in enumerate(mikanani.animes, start=1):
        title = f"{i}. {anime.title}"

        # 创建动漫节点（标题 + 封面）
        msgList.append(
            getNode(MessageSegment.text(title) + MessageSegment.image(anime.cover))
        )

        # 遍历每一集磁力链接
        s = f"{anime.title}"
        for magnet in anime.magnets:
            s += f"\n\n{magnet.episode}\n{magnet.magnet}"
        animeNode.append(getNode(content=Message(MessageSegment.text(s))))

        total += f"\n{title}"

    # 加入主消息列表
    msgList.append(getNode(content=Message(animeNode)))
    # 加入汇总标题
    msgList.insert(0, getNode(Message(total)))
    # 发送嵌套转发消息
    await nonebot.get_bot().send_group_msg(group_id=group_id, message=msgList)


key = on_fullmatch('新番咨询', priority=5)


@key.handle()
async def key_handle(bot: Bot, event: Event):
    await job(event.group_id)


plugin_config = [
    [597015006, Time(9, 10)],  # python群
    [839497425, Time(9, 15)],  # 正人君子
    [616978142, Time(9, 20)],  # 魔怔人
    [868475842, Time(10, 53)],  # 龙河群
    # [974270687, Time(21, 18)],  # 龙河群
]
for gid, t in plugin_config:
    logger.opt(colors=True).info(
        f"<y>{gid}</y> <c>{t}</c>"
    )
    scheduler.add_job(job, "cron", hour=t.hour, minute=t.minute, id=str(time.time_ns()), args=[gid])
