import asyncio
import json
import random
from datetime import date

from nonebot.adapters.onebot.v11 import Bo<PERSON>, Event
from nonebot.adapters.onebot.v11.message import Message
from nonebot.plugin.on import on_fullmatch
from nonebot.plugin import PluginMetadata

__plugin_meta__ = PluginMetadata(
		name="抽签",
		description="看看今天运势怎么样",
		usage="发送‘抽签’即可参加，定时自动撤回",
		type="application",
)

delete_time = 100


def chou_qian(_id):
	random.seed(int(date.today().strftime("%y%m%d")) + _id)
	try:
		with open(r"src/plugins/抽签/抽签2.json", "r", encoding='utf-8') as f:
			s = json.load(f)
			
			# data = s['data']
			# content = random.choice(data)
			# qian = f"\n签头：{content['status']}\n签言：{content['data']}\n签解：{content['meaning']}"
			content = random.choice(s)
			qian = f"\n签头：{content['签头']}\n签身：{content['签身']}\n签诗：{content['签诗']}\n诗意：{content['诗意']}"
			
			return qian
	except Exception as e:
		print(e)
		return "出现错误，请联系管理员，错误信息：{}".format(e)


key = on_fullmatch('抽签')


@key.handle()
async def key_handle(bot: Bot, event: Event):
	id_ = int(event.get_user_id())
	msgid = (await bot.send(event, message=Message(f'[CQ:at,qq={event.get_user_id()}]{chou_qian(id_)}')))['message_id']
	if delete_time:
		await asyncio.sleep(delete_time)
		await bot.call_api('delete_msg', **{
				'message_id': msgid
		})
