import os
import json
import os
import re

from nonebot import on_command, on_message
from nonebot.adapters.onebot.v11 import (
    Bot,
    GroupMessageEvent, PrivateMessageEvent,
    GROUP_OWNER, GROUP_ADMIN
)
from nonebot.exception import ActionFailed
from nonebot.log import logger
from nonebot.permission import SUPERUSER
from nonebot.plugin import PluginMetadata

dataPath = os.path.join(os.path.dirname(__file__), "data") + "/keyword.json"
__plugin_meta__ = PluginMetadata(
    name="违禁词撤回",
    description="自动撤回黑名单中的违禁词语句（如果是管理员）",
    usage="只有超级管理员可以使用",
    type='application',
    extra={
        "author": "wzz",
        "menu_data": [
            {
                "func": "设置违禁词",
                "trigger_method": "命令：设置违禁词+关键词",
                "trigger_condition": "开头匹配[SUPERUSER,GROUP_OWNER,GROUP_ADMIN]",
                "brief_des": "为当前群聊设置违禁词",
                "detail_des": "超级用户及群管理组拥有权限",
            },
        ],
        "menu_template": "default",
    },
)

CustomGroupRetract = on_command("设置违禁词", permission=SUPERUSER | GROUP_OWNER | GROUP_ADMIN, priority=5)


@CustomGroupRetract.handle()
async def register(bot: Bot, event: GroupMessageEvent | PrivateMessageEvent):
    """
    设置违禁词
    群聊：设置违禁词 急了
    私聊：设置违禁词 19263911231 急了
    """
    # 获取群聊号，违禁词
    if isinstance(event, GroupMessageEvent):
        group_id = event.get_session_id().split('_')[1]

        try:
            content = str(event.get_message()).split(maxsplit=1)[1]
        except IndexError:
            await CustomGroupRetract.finish("设置违禁词失败，可能格式错误！")
            return
    else:
        group_id, content = re.search(r'设置违禁词 (\d*) (.*)', str(event.get_message())).groups()

    # 读取配置文件
    try:
        with open(dataPath, 'r', encoding="GBK") as f:
            config = json.load(f)
    except:
        config = {}

    # 更新配置文件中的违禁词
    if group_id in config:
        keyword_list = config[group_id]
        keyword_list.append(content)

        keyword_list = list(set(keyword_list))

    else:
        keyword_list = [content, ]

    # 更新配置文件
    config[group_id] = keyword_list

    # 写入
    with open(dataPath, "w", encoding="GBK") as f:
        json.dump(config, f)

    # 回复消息
    await CustomGroupRetract.finish("成功设置违禁词！")


GroupNotice = on_message(priority=6, block=False)


@GroupNotice.handle()
async def give_notice(bot: Bot, event: GroupMessageEvent):
    # logger.info("进入撤回功能")
    message_id = event.message_id
    group_id = str(event.group_id)

    try:
        # logger.info(group_id)
        with open(dataPath, "r", encoding="GBK") as f:
            cfg = json.load(f)
        keyword_list = cfg[group_id]
        # logger.info("读取成功")
    except (FileNotFoundError, KeyError):
        return

    msg = str(event.get_message())
    logger.info(f"msg={msg}")
    for keyword in keyword_list:
        # logger.info("进入循环")
        if keyword in msg:
            try:
                # logger.info("进入撤回")
                await bot.delete_msg(message_id=message_id)
                logger.info('检测到违禁词，撤回成功')
            except ActionFailed:
                logger.info('检测到违禁词，但权限不足，撤回失败')

        # await GroupNotice.finish()
