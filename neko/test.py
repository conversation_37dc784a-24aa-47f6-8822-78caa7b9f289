import random

from minio import Minio
minio_client = Minio(
    endpoint='192.168.1.5:9768',
    access_key='v5X9grozj0tHoV31dmjh',
    secret_key='agDG6jDLRfmNY8TGZ8TPjaaFV4RnCHXDBRCdZLGt',
    secure=False,
)


for obj in minio_client.list_objects('resource', prefix='dingzhen/', recursive=True):
	name = obj.object_name
	print(name)
	
	img = minio_client.get_object(bucket_name='resource', object_name=name)
	print(img.read().__sizeof__())
	
	
	
	
	break
	
img = random.choice(list(minio_client.list_objects('resource', prefix='dingzhen/', recursive=True)))
print(img.object_name)
	

