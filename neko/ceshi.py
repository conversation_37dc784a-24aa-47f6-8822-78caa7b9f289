import asyncio
from dataclasses import dataclass
from pathlib import Path
from typing import As<PERSON><PERSON><PERSON><PERSON>
from datetime import datetime

import aiohttp
import httpx
from lxml.etree import HTML
import requests
from fpdf import FPDF
from PIL import Image

proxy = "http://192.168.1.3:7890"
transport = httpx.AsyncHTTPTransport(proxy=proxy)
# comic_path = Path('/media/movies/nsfw/comic')
comic_path = Path('/home/<USER>/Downloads')
import warnings
warnings.filterwarnings("ignore", category=UserWarning, module="fpdf.ttfonts")

@dataclass
class Comic:
    title: str
    url: str
    poster_name: Path
    post_time: str
    page_count: str


async def ehentai_popular_comic() -> AsyncIterator[Comic]:
    """
    获取 exhentai.org 的热门本子
    :return: 迭代器
    """
    url = "https://exhentai.org/popular"
    headers = {
        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "cookie": "ipb_member_id=8886661; ipb_pass_hash=537e5abd4c5b79679fb7c4b9bbaf8626; sk=lycw3nzmrnfgdyk8ts5gkii57eq5; star=1-287cfc3348; hath_perks=a.t1.m1-8e6dd304fd; nw=1; ipb_session_id=a7304b7bdc9e3e943ed4c1e2c7358970; event=1754649703"
    }
    async with httpx.AsyncClient(transport=transport, timeout=30) as client:
        response = await client.get(url, headers=headers)
        responseText = response.text
        html = HTML(responseText)

        elements = html.xpath('//table[@class="itg gltc"] | //div[@class="itg gld"]')

        if len(elements) == 0:
            return

        poster_path = comic_path.joinpath('poster')
        poster_path.mkdir(parents=True, exist_ok=True)

        for element in elements[0][1:]:
            title = element.xpath('.//img/@title')[0]
            url = element.xpath('.//td/a/@href | ./a/@href')[0]
            poster = element.xpath('./div[1]/a/img/@src')[-1]
            post_time, page_count = element.xpath('./div/div/div[2]/text()')
            # print(f"{title}\n{url}\n{poster}\n\n")
            # Comic(title='[Merkonig] B-Trayal 48 Darkness (Kono Subarashii Sekai ni Syukufuku o!) [Español] [BigDaddy Scan] [Sin Censura]', url='https://exhentai.org/g/3476159/ecd48697cd/', poster='https://s.exhentai.org/w/01/960/85592-ni58qu3v.webp', post_time='2025-08-09 02:39', page_count='25 pages')

            # 如果封面没有缓存，先缓存
            poster_name = poster_path.joinpath(poster.split('/')[-1])
            if not poster_name.exists():
                print('下载封面')
                poster_response = await client.get(poster)
                with open(poster_name, 'wb') as f:
                    f.write(poster_response.content)

            yield Comic(title, url, poster_name, post_time, page_count)


async def get_popular_comic() -> Path:
    """
    获取热门本子，并创建一个PDF文件保存
    :return: PDF地址
    """
    # 收集所有漫画信息
    comics = []
    async for comic in ehentai_popular_comic():
        comics.append(comic)
        print(comic)

    # 创建PDF文件名（当前日期+时分秒）
    now = datetime.now()
    pdf_filename = f"{now.strftime('%Y%m%d_%H%M%S')}.pdf"
    pdf_path = comic_path.joinpath(pdf_filename)

    # 创建PDF
    pdf = FPDF(unit="pt")  # 使用点作为单位
    pdf.add_font("NotoSansCJK", "", "/usr/share/fonts/truetype/wqy/WenQuanYiMicroHei.ttf", uni=True)
    pdf.set_font("NotoSansCJK", size=12)

    # 设置页面参数
    page_width = 595  # A4页面宽度（点）
    page_height = 842  # A4页面高度（点）
    margin = 40
    content_width = page_width - 2 * margin

    # 每页显示的漫画数量
    comics_per_page = 4

    # 分页处理漫画
    for page_start in range(0, len(comics), comics_per_page):
        pdf.add_page()

        # 页面标题
        pdf.set_font("NotoSansCJK", size=16)
        pdf.cell(content_width, 30, f"热门漫画 - 第{page_start // comics_per_page + 1}页", ln=1, align="C")
        pdf.ln(10)

        # 当前页的漫画
        page_comics = comics[page_start:page_start + comics_per_page]

        for i, comic in enumerate(page_comics):
            comic_index = page_start + i + 1

            # 设置字体大小
            pdf.set_font("NotoSansCJK", size=12)

            # 计算当前漫画在页面中的位置
            y_start = 80 + i * 180  # 每个漫画占用180点高度

            # 漫画序号和标题
            pdf.set_xy(margin, y_start)
            pdf.cell(content_width - 120, 20,
                     f"{comic_index}. {comic.title[:50]}{'...' if len(comic.title) > 50 else ''}", ln=1)

            # URL
            pdf.set_xy(margin, y_start + 25)
            pdf.set_font("NotoSansCJK", size=10)
            pdf.cell(content_width - 120, 15, f"URL: {comic.url[:60]}{'...' if len(comic.url) > 60 else ''}", ln=1)

            # 上传时间和页数
            pdf.set_xy(margin, y_start + 45)
            pdf.cell(content_width - 120, 15, f"上传时间: {comic.post_time}", ln=1)

            pdf.set_xy(margin, y_start + 65)
            pdf.cell(content_width - 120, 15, f"页数: {comic.page_count}", ln=1)

            # 添加封面图片
            try:
                # 打开封面图片
                img = Image.open(comic.poster_name)

                # 计算图片显示尺寸（保持比例，最大100x140点）
                img_width, img_height = img.size
                max_width, max_height = 100, 140

                if img_width > max_width or img_height > max_height:
                    ratio = min(max_width / img_width, max_height / img_height)
                    display_width = int(img_width * ratio)
                    display_height = int(img_height * ratio)
                else:
                    display_width, display_height = img_width, img_height

                # 处理webp格式或其他不支持的格式
                temp_image_path = None
                if comic.poster_name.suffix.lower() in ['.webp', '.bmp', '.gif']:
                    # 转换为JPEG格式
                    temp_image_path = comic.poster_name.parent / f"temp_{comic.poster_name.stem}.jpg"

                    # 如果是RGBA模式，转换为RGB
                    if img.mode in ('RGBA', 'LA', 'P'):
                        # 创建白色背景
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'P':
                            img = img.convert('RGBA')
                        background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                        img = background
                    elif img.mode != 'RGB':
                        img = img.convert('RGB')

                    # 保存为临时JPEG文件
                    img.save(temp_image_path, 'JPEG', quality=85)
                    image_path_for_pdf = str(temp_image_path)
                else:
                    image_path_for_pdf = str(comic.poster_name)

                # 在PDF中添加图片
                img_x = page_width - margin - display_width
                img_y = y_start
                pdf.image(image_path_for_pdf, x=img_x, y=img_y, w=display_width, h=display_height)

                # 删除临时文件
                if temp_image_path and temp_image_path.exists():
                    temp_image_path.unlink()

            except Exception as e:
                print(f"无法添加封面图片 {comic.poster_name}: {e}")
                # 如果图片加载失败，显示占位文本
                pdf.set_xy(page_width - margin - 100, y_start)
                pdf.set_font("NotoSansCJK", size=10)
                pdf.cell(100, 20, "封面加载失败", align="C")

            # 添加分隔线
            if i < len(page_comics) - 1:
                pdf.line(margin, y_start + 160, page_width - margin, y_start + 160)

    # 保存PDF文件
    pdf.output(str(pdf_path))
    print(f"PDF文件已保存到: {pdf_path}")

    return pdf_path


async def main():
    """
    主函数
    :return:
    """
    pdf_path = await get_popular_comic()
    print(f"热门漫画PDF已生成: {pdf_path}")


asyncio.run(main())
