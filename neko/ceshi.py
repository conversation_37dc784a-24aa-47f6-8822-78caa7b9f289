import asyncio
from dataclasses import dataclass
from pathlib import Path
from typing import AsyncIterator

import aiohttp
import httpx
from lxml.etree import HTML
import requests

proxy = "http://192.168.1.3:7890"
transport = httpx.AsyncHTTPTransport(proxy=proxy)
# comic_path = Path('/media/movies/nsfw/comic')
comic_path = Path('/home/<USER>/Downloads')


@dataclass
class Comic:
    title: str
    url: str
    poster_name: Path
    post_time: str
    page_count: str


async def ehentai_popular_comic() -> AsyncIterator[Comic]:
    """
    获取 exhentai.org 的热门本子
    :return: 迭代器
    """
    url = "https://exhentai.org/popular"
    headers = {
        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "cookie": "ipb_member_id=8886661; ipb_pass_hash=537e5abd4c5b79679fb7c4b9bbaf8626; sk=lycw3nzmrnfgdyk8ts5gkii57eq5; star=1-287cfc3348; hath_perks=a.t1.m1-8e6dd304fd; nw=1; ipb_session_id=a7304b7bdc9e3e943ed4c1e2c7358970; event=1754649703"
    }
    async with httpx.AsyncClient(transport=transport, timeout=30) as client:
        response = await client.get(url, headers=headers)
        responseText = response.text
        html = HTML(responseText)

        elements = html.xpath('//table[@class="itg gltc"] | //div[@class="itg gld"]')

        if len(elements) == 0:
            return

        poster_path = comic_path.joinpath('poster')
        poster_path.mkdir(parents=True, exist_ok=True)

        for element in elements[0][1:]:
            title = element.xpath('.//img/@title')[0]
            url = element.xpath('.//td/a/@href | ./a/@href')[0]
            poster = element.xpath('./div[1]/a/img/@src')[-1]
            post_time, page_count = element.xpath('./div/div/div[2]/text()')
            # print(f"{title}\n{url}\n{poster}\n\n")
            # Comic(title='[Merkonig] B-Trayal 48 Darkness (Kono Subarashii Sekai ni Syukufuku o!) [Español] [BigDaddy Scan] [Sin Censura]', url='https://exhentai.org/g/3476159/ecd48697cd/', poster='https://s.exhentai.org/w/01/960/85592-ni58qu3v.webp', post_time='2025-08-09 02:39', page_count='25 pages')

            # 如果封面没有缓存，先缓存
            poster_name = poster_path.joinpath(poster.split('/')[-1])
            if not poster_name.exists():
                poster_response = await client.get(poster)
                with open(poster_name, 'wb') as f:
                    f.write(poster_response.content)

            yield Comic(title, url, poster_name, post_time, page_count)


async def get_popular_comic() -> Path:
    """
    获取热门本子，并创建一个PDF文件保存
    :return: PDF地址
    """
    # async with httpx.AsyncClient(transport=transport, timeout=30) as client:
    async for comic in ehentai_popular_comic():

        print(comic)


async def main():
    """

    :return:
    """
    await get_popular_comic()
    # print(elements)


asyncio.run(main())
